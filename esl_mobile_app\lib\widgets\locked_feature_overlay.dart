import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/trial_status_provider.dart';
import '../constants/app_colors.dart';

class LockedFeatureOverlay extends StatelessWidget {
  final Widget child;
  final String featureName;
  final bool showBlur;
  final String? customMessage;
  final VoidCallback? onUpgradePressed;

  const LockedFeatureOverlay({
    super.key,
    required this.child,
    required this.featureName,
    this.showBlur = true,
    this.customMessage,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<TrialStatusProvider>(
      builder: (context, trialProvider, _) {
        final isLocked = !trialProvider.isFeatureAccessible(featureName.toLowerCase());
        
        if (!isLocked) {
          return child;
        }

        return Stack(
          children: [
            // Original content (blurred if needed)
            if (showBlur)
              ColorFiltered(
                colorFilter: ColorFilter.mode(
                  Colors.grey.withOpacity(0.5),
                  BlendMode.saturation,
                ),
                child: child,
              )
            else
              child,
            
            // Lock overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.teal,
                        size: 32,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Text(
                      trialProvider.isTrialExpired ? 'Access Restricted' : 'Premium Feature',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Text(
                        customMessage ?? 
                        (trialProvider.isTrialExpired 
                            ? 'Your account status is inactive. Upgrade to continue using this feature.'
                            : 'This feature requires a premium subscription.'),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    ElevatedButton(
                      onPressed: onUpgradePressed ?? () {
                        trialProvider.showUpgradeDialog(context, featureName: featureName);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: const Text(
                        'Upgrade Now',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class TrialStatusBanner extends StatelessWidget {
  const TrialStatusBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TrialStatusProvider>(
      builder: (context, trialProvider, _) {
        if (trialProvider.hasFullAccess) {
          return const SizedBox.shrink();
        }

        final daysRemaining = trialProvider.daysRemaining;
        final isExpired = trialProvider.isTrialExpired;
        
        Color bannerColor;
        String message;
        IconData bannerIcon;
        
        if (isExpired) {
          bannerColor = Colors.red;
          message = 'Account inactive - Upgrade to unlock all features';
          bannerIcon = Icons.warning;
        } else if (daysRemaining <= 1) {
          bannerColor = Colors.orange;
          message = 'Trial expires today - Upgrade now!';
          bannerIcon = Icons.schedule;
        } else if (daysRemaining <= 3) {
          bannerColor = Colors.amber;
          message = 'Trial expires in $daysRemaining days';
          bannerIcon = Icons.info;
        } else {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: bannerColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(bannerIcon, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  trialProvider.showUpgradeDialog(context);
                },
                child: const Text(
                  'Upgrade',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
