import 'package:flutter/material.dart';
import '../services/trial_management_service.dart';

class TrialStatusProvider extends ChangeNotifier {
  Map<String, dynamic>? _userPlan;
  bool _isLoading = false;
  DateTime? _lastFetch;
  
  // Cache duration - refresh every 5 minutes
  static const Duration _cacheDuration = Duration(minutes: 5);

  Map<String, dynamic>? get userPlan => _userPlan;
  bool get isLoading => _isLoading;

  // Check if trial is active (status is not inactive)
  bool get isTrialActive {
    if (_userPlan == null) return false;
    return _userPlan!['status'] != 'inactive';
  }

  // Check if trial is expired (status is inactive)
  bool get isTrialExpired {
    if (_userPlan == null) return false;
    return _userPlan!['status'] == 'inactive';
  }

  // Check if user has premium access
  bool get hasPremiumAccess {
    if (_userPlan == null) return false;
    return _userPlan!['plan_id'] == 'premium' || _userPlan!['plan_id'] == 'paid';
  }

  // Check if user has access to all features (status is not inactive)
  bool get hasFullAccess {
    if (_userPlan == null) return false;
    return _userPlan!['status'] != 'inactive';
  }

  // Get days remaining in trial
  int get daysRemaining {
    if (_userPlan == null) return 0;
    return TrialManagementService.getDaysRemaining(_userPlan);
  }

  // Get trial expiry date
  DateTime? get trialExpiryDate {
    if (_userPlan == null) return null;
    return TrialManagementService.getTrialExpiryDate(_userPlan);
  }

  // Features that are always allowed (even with inactive status)
  static const List<String> _allowedFeatures = [
    'home',
    'explore', 
    'roleplay',
    'role_module'
  ];

  // Check if a specific feature is accessible
  bool isFeatureAccessible(String featureName) {
    // Always allow basic features
    if (_allowedFeatures.contains(featureName.toLowerCase())) {
      return true;
    }
    
    // For other features, check if status is not inactive
    if (_userPlan == null) return false;
    return _userPlan!['status'] != 'inactive';
  }

  // Fetch trial status from API
  Future<void> fetchTrialStatus({bool forceRefresh = false}) async {
    // Check if we need to refresh based on cache duration
    if (!forceRefresh && 
        _lastFetch != null && 
        DateTime.now().difference(_lastFetch!) < _cacheDuration &&
        _userPlan != null) {
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      final userPlan = await TrialManagementService.fetchUserPlan();
      _userPlan = userPlan;
      _lastFetch = DateTime.now();
    } catch (e) {
      print('Error fetching trial status: $e');
      // Keep existing data if fetch fails
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh trial status
  Future<void> refreshTrialStatus() async {
    await fetchTrialStatus(forceRefresh: true);
  }

  // Clear trial data (for logout)
  void clearTrialData() {
    _userPlan = null;
    _lastFetch = null;
    notifyListeners();
  }

  // Show upgrade dialog when feature is locked
  void showUpgradeDialog(BuildContext context, {String? featureName}) {
    showDialog(
      context: context,
      builder: (context) => _buildUpgradeDialog(context, featureName),
    );
  }

  Widget _buildUpgradeDialog(BuildContext context, String? featureName) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.teal.shade50,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal, Colors.tealAccent],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.lock,
                color: Colors.white,
                size: 40,
              ),
            ),
            
            const SizedBox(height: 20),
            
            Text(
              isTrialExpired ? 'Trial Expired' : 'Premium Feature',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              isTrialExpired 
                  ? 'Your trial has expired. Upgrade to premium to continue accessing all features!'
                  : featureName != null 
                      ? 'The $featureName feature requires a premium subscription.'
                      : 'This feature requires a premium subscription.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Maybe Later',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // TODO: Navigate to upgrade screen
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Upgrade Now',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
