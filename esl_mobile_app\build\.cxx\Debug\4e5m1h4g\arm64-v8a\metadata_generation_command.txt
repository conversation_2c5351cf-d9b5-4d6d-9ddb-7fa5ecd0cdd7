                        -HC:\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-<PERSON><PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\app\intermediates\cxx\debug\4e5m1h4g\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\app\intermediates\cxx\debug\4e5m1h4g\obj\arm64-v8a
-BC:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\.cxx\debug\4e5m1h4g\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
-DCMAKE_BUILD_TYPE=debug
                        Build command args: []
                        Version: 2