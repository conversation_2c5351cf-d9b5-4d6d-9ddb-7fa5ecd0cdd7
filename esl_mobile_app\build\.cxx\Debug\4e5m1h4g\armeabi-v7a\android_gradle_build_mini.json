{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\.cxx\\debug\\4e5m1h4g\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\.cxx\\debug\\4e5m1h4g\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}