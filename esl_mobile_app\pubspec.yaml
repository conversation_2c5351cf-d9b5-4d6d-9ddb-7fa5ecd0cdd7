name: my_esl_app
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  fluid_bottom_nav_bar: ^1.4.0
  hugeicons: ^0.0.10
  lottie: ^3.0.0
  # http: ^0.13.5
  http: ^1.4.0
  #google_fonts: ^4.0.0
  google_fonts: ^6.2.1
  confetti: ^0.7.0
  flutter_tts: ^3.8.5
  audioplayers: ^5.2.1
  flutter_secure_storage: ^9.2.4
  provider: ^6.1.2
  flutter_html: ^3.0.0-beta.2
  flutter_sound: ^9.25.4
  permission_handler: ^11.4.0
  path_provider: ^2.1.5
  record: ^5.2.1  # Updated to latest version with AGP 8+ support
  # record_linux: ^0.0.1-placeholder  # This effectively disables the Linux implementation
  razorpay_flutter: ^1.4.0
  carousel_slider: ^5.0.0
  sms_autofill: ^2.4.1
  speech_to_text: ^7.0.0
  pinput: ^5.0.1
  file_picker: ^10.1.0
  image_picker: ^1.1.1
  url_launcher: ^6.3.1
  flutter_social_button: ^1.1.6+1
  glassmorphism: ^3.0.0
  circular_countdown_timer: ^0.2.3
  flutter_spin_wheel_menu: ^0.0.4
  font_awesome_flutter: ^10.8.0
  star_menu: ^4.0.1
  responsive_framework: ^1.5.1
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.4.1
  model_viewer_plus: ^1.9.0
  flutter_cube: ^0.1.1
  webview_flutter: ^4.7.0
  o3d: ^3.1.3
  flutter_inappwebview: ^6.1.5
  flutter_automation: ^2.0.0
  chewie: ^1.11.3  # Temporarily disabled due to wakelock_plus compatibility issues
  video_player: ^2.9.5
  # mongo_dart: ^0.10.5
  mongo_dart: ^0.10.3
  animate_do: ^4.2.0
  # mic_stream: ^0.7.2  # Temporarily disabled due to compatibility issues
  particles_flutter: ^1.0.1
  tutorial_coach_mark: ^1.3.0
  shared_preferences: ^2.2.2
  flutter_animate: ^4.5.0
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  flutter_local_notifications: ^19.2.1
  country_code_picker: ^2.0.2
  intl_phone_field: ^3.2.0
  smooth_page_indicator: ^1.2.1
  dart_openai: ^5.1.0
  flutter_dotenv: ^5.2.1
  flutter_fortune_wheel: ^1.2.0
  animated_text_kit: ^4.2.2
  card_swiper: ^2.0.4
  cached_network_image: ^3.3.1
  flip_card: ^0.7.0
  flutter_svg: ^1.1.6
  




dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutterfire_cli: ^1.2.0

dependency_overrides:
  record_linux: 1.1.1  # Force compatible version for all packages
  http: ^1.2.1  # Forces this version for all packages
  

flutter:
  uses-material-design: true
  assets:
    # Environment file
    - .env
    
    # Data files
    - assets/questions.json
    - assets/quizData.json
    
    # Root level assets
    - assets/aceimg.png
    - assets/acelogs.jpg
    - assets/ace1img.jpg
    - assets/bg.png
    - assets/bg_1.png
    - assets/bg_2.png
    - assets/books_1.png
    - assets/chat.png
    - assets/clouds_1.png
    - assets/galaxy.jpg
    - assets/game-play.png
    - assets/getstarted_btn.png
    - assets/il4.jpg
    - assets/il8.jpg
    - assets/starry_galxy.jpg
    - assets/user.png
    
    # SVG files
    - assets/BG BOOK.svg
    - assets/simple_card_bg.svg
    - assets/Vocabulary_card_bg.svg
    
    # Include all files in subdirectories
    - assets/animations/
    - assets/icons/
    - assets/images/
    - assets/models/
    - assets/sounds/
    - assets/web/ 



    


flutter_icons:
  android: true
  ios: true
  image_path: assets/acelogs.jpg
  adaptive_icon_background: "#E6E1F2"

flutter_native_splash:
  color: "white" # Teal green background color
  image: assets/acelogs.jpg
  android: true
  ios: true
  android_gravity: center_crop
  ios_content_mode: scaleAspectFill
  fullscreen: true
