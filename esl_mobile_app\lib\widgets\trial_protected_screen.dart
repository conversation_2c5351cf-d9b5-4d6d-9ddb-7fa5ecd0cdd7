import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../main.dart';
import '../constants/app_colors.dart';
import '../services/trial_management_service.dart';
import 'premium_content_lock.dart';

class TrialProtectedScreen extends StatefulWidget {
  final Widget child;
  final String screenName;
  final String? customLockMessage;
  final bool allowBasicAccess;
  
  const TrialProtectedScreen({
    super.key,
    required this.child,
    required this.screenName,
    this.customLockMessage,
    this.allowBasicAccess = false,
  });

  @override
  State<TrialProtectedScreen> createState() => _TrialProtectedScreenState();
}

class _TrialProtectedScreenState extends State<TrialProtectedScreen> {
  Map<String, dynamic>? _userPlan;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkTrialStatus();
  }

  Future<void> _checkTrialStatus() async {
    try {
      final userPlan = await TrialManagementService.fetchUserPlan();
      if (mounted) {
        setState(() {
          _userPlan = userPlan;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _shouldLockContent() {
    if (_userPlan == null) return true;

    // If basic access is allowed, only lock if status is inactive
    if (widget.allowBasicAccess) {
      return TrialManagementService.isTrialExpired(_userPlan);
    }

    // Otherwise, lock if status is inactive (regardless of plan type)
    return TrialManagementService.isTrialExpired(_userPlan);
  }

  void _handleUpgradePressed() {
    // Navigate to upgrade screen or show upgrade modal
    showDialog(
      context: context,
      builder: (context) => _buildUpgradeDialog(),
    );
  }

  Widget _buildUpgradeDialog() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode 
              ? AppColors.darkBackground 
              : AppColors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: AppColors.tealGradient,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.star,
                color: Colors.white,
                size: 40,
              ),
            ),
            
            const SizedBox(height: 20),
            
            Text(
              'Upgrade to Premium',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode 
                    ? Colors.white 
                    : AppColors.primary,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              'Unlock unlimited access to all features and content!',
              style: TextStyle(
                fontSize: 16,
                color: themeProvider.isDarkMode 
                    ? Colors.grey[300] 
                    : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // Premium features list
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _buildFeatureItem('✓ Unlimited access to all modules'),
                  _buildFeatureItem('✓ Advanced learning features'),
                  _buildFeatureItem('✓ Personalized content'),
                  _buildFeatureItem('✓ Priority support'),
                  _buildFeatureItem('✓ Offline access'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Maybe Later',
                      style: TextStyle(
                        color: themeProvider.isDarkMode 
                            ? Colors.grey[400] 
                            : Colors.grey[600],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Navigate to actual upgrade screen
                      _navigateToUpgradeScreen();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Upgrade Now',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text.substring(2), // Remove the ✓ symbol
              style: TextStyle(
                fontSize: 14,
                color: themeProvider.isDarkMode 
                    ? Colors.grey[300] 
                    : Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToUpgradeScreen() {
    // This would navigate to the actual upgrade/pro screen
    // For now, we'll just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Upgrade screen would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      );
    }

    final isLocked = _shouldLockContent();
    
    if (isLocked) {
      return Scaffold(
        body: PremiumContentLock(
          isLocked: true,
          customMessage: widget.customLockMessage ?? 
              'This ${widget.screenName} feature requires a premium subscription. '
              'Upgrade now to unlock unlimited access!',
          onUpgradePressed: _handleUpgradePressed,
          showBlur: false,
          child: Container(), // Empty container since content is locked
        ),
      );
    }

    return widget.child;
  }
}

// Helper widget for adding premium badges to UI elements
class TrialStatusIndicator extends StatelessWidget {
  final Map<String, dynamic>? userPlan;
  
  const TrialStatusIndicator({super.key, this.userPlan});

  @override
  Widget build(BuildContext context) {
    if (userPlan == null) return const SizedBox.shrink();
    
    final isTrialActive = TrialManagementService.isTrialActive(userPlan);
    final isExpired = TrialManagementService.isTrialExpired(userPlan);
    final daysRemaining = TrialManagementService.getDaysRemaining(userPlan);
    
    if (isExpired) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'EXPIRED',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
    
    if (isTrialActive) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: daysRemaining <= 1 ? Colors.orange : AppColors.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          'TRIAL: ${daysRemaining}d',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
    
    return const SizedBox.shrink();
  }
}
