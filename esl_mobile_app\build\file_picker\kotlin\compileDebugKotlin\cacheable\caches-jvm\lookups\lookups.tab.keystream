  Activity android.app  Application android.app  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  applicationContext android.app.Activity  packageManager android.app.Activity  startActivityForResult android.app.Activity  ActivityLifecycleCallbacks android.app.Application  $unregisterActivityLifecycleCallbacks android.app.Application  ClipData android.content  Context android.content  Intent android.content  	getItemAt android.content.ClipData  	itemCount android.content.ClipData  uri android.content.ClipData.Item  getType android.content.ContentResolver  openInputStream android.content.ContentResolver  openOutputStream android.content.ContentResolver  query android.content.ContentResolver  cacheDir android.content.Context  contentResolver android.content.Context  let android.content.Context  applicationContext android.content.ContextWrapper  packageManager android.content.ContextWrapper  ACTION_CREATE_DOCUMENT android.content.Intent  ACTION_OPEN_DOCUMENT android.content.Intent  ACTION_OPEN_DOCUMENT_TREE android.content.Intent  ACTION_PICK android.content.Intent  CATEGORY_OPENABLE android.content.Intent  EXTRA_ALLOW_MULTIPLE android.content.Intent  EXTRA_MIME_TYPES android.content.Intent  EXTRA_TITLE android.content.Intent  Intent android.content.Intent  addCategory android.content.Intent  allowedExtensions android.content.Intent  apply android.content.Intent  clipData android.content.Intent  data android.content.Intent  extras android.content.Intent  isMultipleSelection android.content.Intent  let android.content.Intent  putExtra android.content.Intent  resolveActivity android.content.Intent  setDataAndType android.content.Intent  toTypedArray android.content.Intent  type android.content.Intent  Cursor android.database  getColumnIndexOrThrow android.database.Cursor  	getString android.database.Cursor  moveToFirst android.database.Cursor  use android.database.Cursor  Bitmap android.graphics  
BitmapFactory android.graphics  CompressFormat android.graphics.Bitmap  compress android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  PNG &android.graphics.Bitmap.CompressFormat  WEBP &android.graphics.Bitmap.CompressFormat  decodeStream android.graphics.BitmapFactory  Uri android.net  	authority android.net.Uri  fromFile android.net.Uri  path android.net.Uri  scheme android.net.Uri  toString android.net.Uri  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  
Parcelable 
android.os  containsKey android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  containsKey android.os.Bundle  getParcelableArrayList android.os.Bundle  DIRECTORY_DOWNLOADS android.os.Environment  getExternalStorageDirectory android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  post android.os.Handler  
getMainLooper android.os.Looper  DocumentsContract android.provider  OpenableColumns android.provider  EXTRA_INITIAL_URI "android.provider.DocumentsContract  buildDocumentUriUsingTree "android.provider.DocumentsContract  
getDocumentId "android.provider.DocumentsContract  getTreeDocumentId "android.provider.DocumentsContract  DISPLAY_NAME  android.provider.OpenableColumns  Log android.util  e android.util.Log  w android.util.Log  MimeTypeMap android.webkit  getExtensionFromMimeType android.webkit.MimeTypeMap  getMimeTypeFromExtension android.webkit.MimeTypeMap  getSingleton android.webkit.MimeTypeMap  toUri androidx.core.net  DefaultLifecycleObserver androidx.lifecycle  	Lifecycle androidx.lifecycle  LifecycleOwner androidx.lifecycle  addObserver androidx.lifecycle.Lifecycle  removeObserver androidx.lifecycle.Lifecycle  Activity  com.mr.flutter.plugin.filepicker  
ActivityAware  com.mr.flutter.plugin.filepicker  ActivityPluginBinding  com.mr.flutter.plugin.filepicker  ActivityResultListener  com.mr.flutter.plugin.filepicker  Any  com.mr.flutter.plugin.filepicker  Application  com.mr.flutter.plugin.filepicker  	ArrayList  com.mr.flutter.plugin.filepicker  BinaryMessenger  com.mr.flutter.plugin.filepicker  Bitmap  com.mr.flutter.plugin.filepicker  
BitmapFactory  com.mr.flutter.plugin.filepicker  Boolean  com.mr.flutter.plugin.filepicker  BufferedInputStream  com.mr.flutter.plugin.filepicker  BufferedOutputStream  com.mr.flutter.plugin.filepicker  Build  com.mr.flutter.plugin.filepicker  Builder  com.mr.flutter.plugin.filepicker  Bundle  com.mr.flutter.plugin.filepicker  	ByteArray  com.mr.flutter.plugin.filepicker  CHANNEL  com.mr.flutter.plugin.filepicker  Context  com.mr.flutter.plugin.filepicker  CoroutineScope  com.mr.flutter.plugin.filepicker  Date  com.mr.flutter.plugin.filepicker  DefaultLifecycleObserver  com.mr.flutter.plugin.filepicker  Dispatchers  com.mr.flutter.plugin.filepicker  DocumentsContract  com.mr.flutter.plugin.filepicker  
EVENT_CHANNEL  com.mr.flutter.plugin.filepicker  Environment  com.mr.flutter.plugin.filepicker  EventChannel  com.mr.flutter.plugin.filepicker  	EventSink  com.mr.flutter.plugin.filepicker  	Exception  com.mr.flutter.plugin.filepicker  File  com.mr.flutter.plugin.filepicker  FileInfo  com.mr.flutter.plugin.filepicker  FileInputStream  com.mr.flutter.plugin.filepicker  FileNotFoundException  com.mr.flutter.plugin.filepicker  FileOutputStream  com.mr.flutter.plugin.filepicker  FilePickerDelegate  com.mr.flutter.plugin.filepicker  FilePickerPlugin  com.mr.flutter.plugin.filepicker  	FileUtils  com.mr.flutter.plugin.filepicker  FlutterLifecycleAdapter  com.mr.flutter.plugin.filepicker  
FlutterPlugin  com.mr.flutter.plugin.filepicker  FlutterPluginBinding  com.mr.flutter.plugin.filepicker  Handler  com.mr.flutter.plugin.filepicker  HashMap  com.mr.flutter.plugin.filepicker  IOException  com.mr.flutter.plugin.filepicker  InputStream  com.mr.flutter.plugin.filepicker  Int  com.mr.flutter.plugin.filepicker  Intent  com.mr.flutter.plugin.filepicker  	JvmStatic  com.mr.flutter.plugin.filepicker  LifeCycleObserver  com.mr.flutter.plugin.filepicker  	Lifecycle  com.mr.flutter.plugin.filepicker  LifecycleOwner  com.mr.flutter.plugin.filepicker  List  com.mr.flutter.plugin.filepicker  Locale  com.mr.flutter.plugin.filepicker  Log  com.mr.flutter.plugin.filepicker  Long  com.mr.flutter.plugin.filepicker  Looper  com.mr.flutter.plugin.filepicker  Metadata  com.mr.flutter.plugin.filepicker  
MethodCall  com.mr.flutter.plugin.filepicker  MethodCallHandler  com.mr.flutter.plugin.filepicker  
MethodChannel  com.mr.flutter.plugin.filepicker  MethodResultWrapper  com.mr.flutter.plugin.filepicker  MimeTypeMap  com.mr.flutter.plugin.filepicker  MutableList  com.mr.flutter.plugin.filepicker  OpenableColumns  com.mr.flutter.plugin.filepicker  Pair  com.mr.flutter.plugin.filepicker  
Parcelable  com.mr.flutter.plugin.filepicker  REQUEST_CODE  com.mr.flutter.plugin.filepicker  RuntimeException  com.mr.flutter.plugin.filepicker  SAVE_FILE_CODE  com.mr.flutter.plugin.filepicker  SimpleDateFormat  com.mr.flutter.plugin.filepicker  String  com.mr.flutter.plugin.filepicker  Suppress  com.mr.flutter.plugin.filepicker  System  com.mr.flutter.plugin.filepicker  TAG  com.mr.flutter.plugin.filepicker  	Throwable  com.mr.flutter.plugin.filepicker  Throws  com.mr.flutter.plugin.filepicker  Tika  com.mr.flutter.plugin.filepicker  TikaCoreProperties  com.mr.flutter.plugin.filepicker  TikaInputStream  com.mr.flutter.plugin.filepicker  Uri  com.mr.flutter.plugin.filepicker  addFile  com.mr.flutter.plugin.filepicker  allowedExtensions  com.mr.flutter.plugin.filepicker  also  com.mr.flutter.plugin.filepicker  apply  com.mr.flutter.plugin.filepicker  arrayOf  com.mr.flutter.plugin.filepicker  
clearCache  com.mr.flutter.plugin.filepicker  contains  com.mr.flutter.plugin.filepicker  
contentEquals  com.mr.flutter.plugin.filepicker  
dropLastWhile  com.mr.flutter.plugin.filepicker  endsWith  com.mr.flutter.plugin.filepicker  equals  com.mr.flutter.plugin.filepicker  filter  com.mr.flutter.plugin.filepicker  filterIsInstance  com.mr.flutter.plugin.filepicker  finishWithAlreadyActiveError  com.mr.flutter.plugin.filepicker  finishWithError  com.mr.flutter.plugin.filepicker  finishWithSuccess  com.mr.flutter.plugin.filepicker  forEach  com.mr.flutter.plugin.filepicker  get  com.mr.flutter.plugin.filepicker  getFileExtension  com.mr.flutter.plugin.filepicker  getFullPathFromTreeUri  com.mr.flutter.plugin.filepicker  getMimeTypeForBytes  com.mr.flutter.plugin.filepicker  getMimeTypes  com.mr.flutter.plugin.filepicker  getSelectedItems  com.mr.flutter.plugin.filepicker  handleFileResult  com.mr.flutter.plugin.filepicker  	hashMapOf  com.mr.flutter.plugin.filepicker  indices  com.mr.flutter.plugin.filepicker  isEmpty  com.mr.flutter.plugin.filepicker  isMultipleSelection  com.mr.flutter.plugin.filepicker  
isNotEmpty  com.mr.flutter.plugin.filepicker  
isNullOrEmpty  com.mr.flutter.plugin.filepicker  java  com.mr.flutter.plugin.filepicker  last  com.mr.flutter.plugin.filepicker  launch  com.mr.flutter.plugin.filepicker  let  com.mr.flutter.plugin.filepicker  
mapNotNull  com.mr.flutter.plugin.filepicker  matches  com.mr.flutter.plugin.filepicker  
mutableListOf  com.mr.flutter.plugin.filepicker  orEmpty  com.mr.flutter.plugin.filepicker  
processUri  com.mr.flutter.plugin.filepicker  resolveType  com.mr.flutter.plugin.filepicker  saveFile  com.mr.flutter.plugin.filepicker  split  com.mr.flutter.plugin.filepicker  startFileExplorer  com.mr.flutter.plugin.filepicker  
startsWith  com.mr.flutter.plugin.filepicker  	substring  com.mr.flutter.plugin.filepicker  substringAfter  com.mr.flutter.plugin.filepicker  substringAfterLast  com.mr.flutter.plugin.filepicker  takeIf  com.mr.flutter.plugin.filepicker  toRegex  com.mr.flutter.plugin.filepicker  toString  com.mr.flutter.plugin.filepicker  toTypedArray  com.mr.flutter.plugin.filepicker  toUri  com.mr.flutter.plugin.filepicker  until  com.mr.flutter.plugin.filepicker  	uppercase  com.mr.flutter.plugin.filepicker  use  com.mr.flutter.plugin.filepicker  writeBytesData  com.mr.flutter.plugin.filepicker  ActivityLifecycleCallbacks ,com.mr.flutter.plugin.filepicker.Application  CompressFormat 'com.mr.flutter.plugin.filepicker.Bitmap  
StreamHandler -com.mr.flutter.plugin.filepicker.EventChannel  Any )com.mr.flutter.plugin.filepicker.FileInfo  Builder )com.mr.flutter.plugin.filepicker.FileInfo  	ByteArray )com.mr.flutter.plugin.filepicker.FileInfo  FileInfo )com.mr.flutter.plugin.filepicker.FileInfo  HashMap )com.mr.flutter.plugin.filepicker.FileInfo  Long )com.mr.flutter.plugin.filepicker.FileInfo  Pair )com.mr.flutter.plugin.filepicker.FileInfo  String )com.mr.flutter.plugin.filepicker.FileInfo  Uri )com.mr.flutter.plugin.filepicker.FileInfo  bytes )com.mr.flutter.plugin.filepicker.FileInfo  	hashMapOf )com.mr.flutter.plugin.filepicker.FileInfo  let )com.mr.flutter.plugin.filepicker.FileInfo  name )com.mr.flutter.plugin.filepicker.FileInfo  path )com.mr.flutter.plugin.filepicker.FileInfo  size )com.mr.flutter.plugin.filepicker.FileInfo  toMap )com.mr.flutter.plugin.filepicker.FileInfo  toString )com.mr.flutter.plugin.filepicker.FileInfo  uri )com.mr.flutter.plugin.filepicker.FileInfo  FileInfo 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  build 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  bytes 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  name 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  path 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  size 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  uri 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  withData 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  withName 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  withPath 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  withSize 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  withUri 1com.mr.flutter.plugin.filepicker.FileInfo.Builder  Activity 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Any 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  	ArrayList 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Boolean 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Build 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  	ByteArray 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  	Companion 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  CoroutineScope 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Dispatchers 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  DocumentsContract 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Environment 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  	EventSink 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  File 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  FileInfo 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  FilePickerDelegate 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  FilePickerPlugin 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  	FileUtils 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Handler 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  IOException 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Int 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Intent 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Log 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Looper 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
MethodChannel 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  REQUEST_CODE 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  SAVE_FILE_CODE 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  String 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  TAG 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Uri 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  activity 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  addFile 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  allowedExtensions 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  also 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  apply 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  bytes 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  clearPendingResult 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  compressionQuality 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  contains 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  dispatchEventStatus 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  	eventSink 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  filter 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  filterIsInstance 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  finishWithAlreadyActiveError 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  finishWithError 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  finishWithSuccess 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  getFullPathFromTreeUri 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  getMimeTypeForBytes 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  getSelectedItems 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  handleFilePickerResult 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  handleFileResult 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  handleSaveFileResult 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  isMultipleSelection 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
isNotEmpty 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
isNullOrEmpty 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  java 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  launch 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  let 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  loadDataToMemory 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
mapNotNull 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
mutableListOf 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  orEmpty 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
pendingResult 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  processFiles 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  
processUri 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  saveFile 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  setEventHandler 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  setPendingMethodCallResult 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  split 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  startFileExplorer 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  takeIf 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  toTypedArray 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  toUri 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  type 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  until 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  writeBytesData 3com.mr.flutter.plugin.filepicker.FilePickerDelegate  Activity =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  FilePickerPlugin =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  	FileUtils =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  Handler =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  Log =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  Looper =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  REQUEST_CODE =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  SAVE_FILE_CODE =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  TAG =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  also =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  finishWithAlreadyActiveError =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  java =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  let =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  
mapNotNull =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  orEmpty =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  processFiles =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  takeIf =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  writeBytesData =com.mr.flutter.plugin.filepicker.FilePickerDelegate.Companion  Result Acom.mr.flutter.plugin.filepicker.FilePickerDelegate.MethodChannel  Activity 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  ActivityPluginBinding 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  Any 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  Application 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  	ArrayList 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  BinaryMessenger 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  Boolean 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  Bundle 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  	ByteArray 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  CHANNEL 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  	Companion 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  DefaultLifecycleObserver 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
EVENT_CHANNEL 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  EventChannel 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  	EventSink 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  FilePickerDelegate 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  FlutterLifecycleAdapter 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  FlutterPluginBinding 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  HashMap 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  Int 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  LifeCycleObserver 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  	Lifecycle 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  LifecycleOwner 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
MethodCall 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
MethodChannel 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  MethodResultWrapper 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  String 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  TAG 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  activity 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  activityBinding 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  application 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  channel 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
clearCache 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  contains 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  delegate 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  get 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  getFileExtension 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  getMimeTypes 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
isNotEmpty 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
isNullOrEmpty 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  let 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  	lifecycle 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  observer 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  onAttachedToActivity 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  onDetachedFromActivity 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  
pluginBinding 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  resolveType 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  saveFile 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  setup 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  startFileExplorer 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  tearDown 1com.mr.flutter.plugin.filepicker.FilePickerPlugin  ActivityLifecycleCallbacks =com.mr.flutter.plugin.filepicker.FilePickerPlugin.Application  CHANNEL ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  
EVENT_CHANNEL ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  EventChannel ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  FilePickerDelegate ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  FlutterLifecycleAdapter ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  
MethodChannel ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  MethodResultWrapper ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  TAG ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  
clearCache ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  contains ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  get ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  getFileExtension ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  getMimeTypes ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  
isNotEmpty ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  
isNullOrEmpty ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  let ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  resolveType ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  saveFile ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  startFileExplorer ;com.mr.flutter.plugin.filepicker.FilePickerPlugin.Companion  
StreamHandler >com.mr.flutter.plugin.filepicker.FilePickerPlugin.EventChannel  let Ccom.mr.flutter.plugin.filepicker.FilePickerPlugin.LifeCycleObserver  onActivityDestroyed Ccom.mr.flutter.plugin.filepicker.FilePickerPlugin.LifeCycleObserver  onActivityStopped Ccom.mr.flutter.plugin.filepicker.FilePickerPlugin.LifeCycleObserver  thisActivity Ccom.mr.flutter.plugin.filepicker.FilePickerPlugin.LifeCycleObserver  Result ?com.mr.flutter.plugin.filepicker.FilePickerPlugin.MethodChannel  	ArrayList *com.mr.flutter.plugin.filepicker.FileUtils  Bitmap *com.mr.flutter.plugin.filepicker.FileUtils  
BitmapFactory *com.mr.flutter.plugin.filepicker.FileUtils  BufferedInputStream *com.mr.flutter.plugin.filepicker.FileUtils  BufferedOutputStream *com.mr.flutter.plugin.filepicker.FileUtils  Build *com.mr.flutter.plugin.filepicker.FileUtils  	ByteArray *com.mr.flutter.plugin.filepicker.FileUtils  CoroutineScope *com.mr.flutter.plugin.filepicker.FileUtils  Date *com.mr.flutter.plugin.filepicker.FileUtils  Dispatchers *com.mr.flutter.plugin.filepicker.FileUtils  DocumentsContract *com.mr.flutter.plugin.filepicker.FileUtils  Environment *com.mr.flutter.plugin.filepicker.FileUtils  File *com.mr.flutter.plugin.filepicker.FileUtils  FileInfo *com.mr.flutter.plugin.filepicker.FileUtils  FileInputStream *com.mr.flutter.plugin.filepicker.FileUtils  FileOutputStream *com.mr.flutter.plugin.filepicker.FileUtils  FilePickerDelegate *com.mr.flutter.plugin.filepicker.FileUtils  IOException *com.mr.flutter.plugin.filepicker.FileUtils  Intent *com.mr.flutter.plugin.filepicker.FileUtils  Locale *com.mr.flutter.plugin.filepicker.FileUtils  Log *com.mr.flutter.plugin.filepicker.FileUtils  Metadata *com.mr.flutter.plugin.filepicker.FileUtils  MimeTypeMap *com.mr.flutter.plugin.filepicker.FileUtils  OpenableColumns *com.mr.flutter.plugin.filepicker.FileUtils  
Parcelable *com.mr.flutter.plugin.filepicker.FileUtils  REQUEST_CODE *com.mr.flutter.plugin.filepicker.FileUtils  RuntimeException *com.mr.flutter.plugin.filepicker.FileUtils  SAVE_FILE_CODE *com.mr.flutter.plugin.filepicker.FileUtils  SimpleDateFormat *com.mr.flutter.plugin.filepicker.FileUtils  System *com.mr.flutter.plugin.filepicker.FileUtils  TAG *com.mr.flutter.plugin.filepicker.FileUtils  Tika *com.mr.flutter.plugin.filepicker.FileUtils  TikaCoreProperties *com.mr.flutter.plugin.filepicker.FileUtils  TikaInputStream *com.mr.flutter.plugin.filepicker.FileUtils  Uri *com.mr.flutter.plugin.filepicker.FileUtils  addFile *com.mr.flutter.plugin.filepicker.FileUtils  allowedExtensions *com.mr.flutter.plugin.filepicker.FileUtils  also *com.mr.flutter.plugin.filepicker.FileUtils  apply *com.mr.flutter.plugin.filepicker.FileUtils  arrayOf *com.mr.flutter.plugin.filepicker.FileUtils  
clearCache *com.mr.flutter.plugin.filepicker.FileUtils  
compressImage *com.mr.flutter.plugin.filepicker.FileUtils  contains *com.mr.flutter.plugin.filepicker.FileUtils  
contentEquals *com.mr.flutter.plugin.filepicker.FileUtils  createImageFile *com.mr.flutter.plugin.filepicker.FileUtils  
dropLastWhile *com.mr.flutter.plugin.filepicker.FileUtils  endsWith *com.mr.flutter.plugin.filepicker.FileUtils  equals *com.mr.flutter.plugin.filepicker.FileUtils  filter *com.mr.flutter.plugin.filepicker.FileUtils  filterIsInstance *com.mr.flutter.plugin.filepicker.FileUtils  finishWithAlreadyActiveError *com.mr.flutter.plugin.filepicker.FileUtils  finishWithError *com.mr.flutter.plugin.filepicker.FileUtils  finishWithSuccess *com.mr.flutter.plugin.filepicker.FileUtils  getCompressFormat *com.mr.flutter.plugin.filepicker.FileUtils  getDocumentPathFromTreeUri *com.mr.flutter.plugin.filepicker.FileUtils  getFileExtension *com.mr.flutter.plugin.filepicker.FileUtils  getFileName *com.mr.flutter.plugin.filepicker.FileUtils  getFullPathFromTreeUri *com.mr.flutter.plugin.filepicker.FileUtils  getMimeTypeForBytes *com.mr.flutter.plugin.filepicker.FileUtils  getMimeTypes *com.mr.flutter.plugin.filepicker.FileUtils  getPathFromTreeUri *com.mr.flutter.plugin.filepicker.FileUtils  getSelectedItems *com.mr.flutter.plugin.filepicker.FileUtils  handleFileResult *com.mr.flutter.plugin.filepicker.FileUtils  indices *com.mr.flutter.plugin.filepicker.FileUtils  isDownloadsDocument *com.mr.flutter.plugin.filepicker.FileUtils  isEmpty *com.mr.flutter.plugin.filepicker.FileUtils  isImage *com.mr.flutter.plugin.filepicker.FileUtils  isMultipleSelection *com.mr.flutter.plugin.filepicker.FileUtils  
isNotEmpty *com.mr.flutter.plugin.filepicker.FileUtils  
isNullOrEmpty *com.mr.flutter.plugin.filepicker.FileUtils  java *com.mr.flutter.plugin.filepicker.FileUtils  last *com.mr.flutter.plugin.filepicker.FileUtils  launch *com.mr.flutter.plugin.filepicker.FileUtils  let *com.mr.flutter.plugin.filepicker.FileUtils  loadData *com.mr.flutter.plugin.filepicker.FileUtils  matches *com.mr.flutter.plugin.filepicker.FileUtils  
mutableListOf *com.mr.flutter.plugin.filepicker.FileUtils  openFileStream *com.mr.flutter.plugin.filepicker.FileUtils  orEmpty *com.mr.flutter.plugin.filepicker.FileUtils  processFiles *com.mr.flutter.plugin.filepicker.FileUtils  
processUri *com.mr.flutter.plugin.filepicker.FileUtils  recursiveDeleteFile *com.mr.flutter.plugin.filepicker.FileUtils  saveFile *com.mr.flutter.plugin.filepicker.FileUtils  split *com.mr.flutter.plugin.filepicker.FileUtils  startFileExplorer *com.mr.flutter.plugin.filepicker.FileUtils  
startsWith *com.mr.flutter.plugin.filepicker.FileUtils  	substring *com.mr.flutter.plugin.filepicker.FileUtils  substringAfter *com.mr.flutter.plugin.filepicker.FileUtils  substringAfterLast *com.mr.flutter.plugin.filepicker.FileUtils  takeIf *com.mr.flutter.plugin.filepicker.FileUtils  toRegex *com.mr.flutter.plugin.filepicker.FileUtils  toTypedArray *com.mr.flutter.plugin.filepicker.FileUtils  toUri *com.mr.flutter.plugin.filepicker.FileUtils  until *com.mr.flutter.plugin.filepicker.FileUtils  	uppercase *com.mr.flutter.plugin.filepicker.FileUtils  use *com.mr.flutter.plugin.filepicker.FileUtils  writeBytesData *com.mr.flutter.plugin.filepicker.FileUtils  Result .com.mr.flutter.plugin.filepicker.MethodChannel  Handler 4com.mr.flutter.plugin.filepicker.MethodResultWrapper  Looper 4com.mr.flutter.plugin.filepicker.MethodResultWrapper  handler 4com.mr.flutter.plugin.filepicker.MethodResultWrapper  methodResult 4com.mr.flutter.plugin.filepicker.MethodResultWrapper  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  let Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  removeActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  FlutterLifecycleAdapter -io.flutter.embedding.engine.plugins.lifecycle  getActivityLifecycle Eio.flutter.embedding.engine.plugins.lifecycle.FlutterLifecycleAdapter  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  let -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  BufferedInputStream java.io  BufferedOutputStream java.io  File java.io  FileDescriptor java.io  FileInputStream java.io  FileNotFoundException java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  OutputStream java.io  close java.io.BufferedInputStream  read java.io.BufferedInputStream  flush java.io.BufferedOutputStream  write java.io.BufferedOutputStream  absolutePath java.io.File  createTempFile java.io.File  delete java.io.File  exists java.io.File  isDirectory java.io.File  length java.io.File  	listFiles java.io.File  mkdirs java.io.File  
parentFile java.io.File  path java.io.File  	separator java.io.File  toString java.io.File  sync java.io.FileDescriptor  message java.io.FileNotFoundException  close java.io.FileOutputStream  fd java.io.FileOutputStream  flush java.io.FileOutputStream  read java.io.FilterInputStream  write java.io.FilterOutputStream  message java.io.IOException  close java.io.InputStream  read java.io.InputStream  use java.io.InputStream  flush java.io.OutputStream  use java.io.OutputStream  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  RuntimeException 	java.lang  hashCode java.lang.Class  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	ArrayList 	java.util  Date 	java.util  HashMap 	java.util  Locale 	java.util  add java.util.ArrayList  filterIsInstance java.util.ArrayList  get java.util.ArrayList  indices java.util.ArrayList  
isNullOrEmpty java.util.ArrayList  let java.util.ArrayList  
mapNotNull java.util.ArrayList  toTypedArray java.util.ArrayList  get java.util.HashMap  
getDefault java.util.Locale  Array kotlin  	ByteArray kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  Suppress kotlin  	Throwable kotlin  also kotlin  apply kotlin  arrayOf kotlin  let kotlin  takeIf kotlin  toString kotlin  use kotlin  equals 
kotlin.Any  hashCode 
kotlin.Any  takeIf 
kotlin.Any  toString 
kotlin.Any  get kotlin.Array  iterator kotlin.Array  size kotlin.Array  also kotlin.Boolean  not kotlin.Boolean  let kotlin.ByteArray  size kotlin.ByteArray  also 
kotlin.Int  and 
kotlin.Int  	compareTo 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  toInt kotlin.Long  contains 
kotlin.String  
contentEquals 
kotlin.String  endsWith 
kotlin.String  equals 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  orEmpty 
kotlin.String  plus 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  substringAfterLast 
kotlin.String  takeIf 
kotlin.String  toRegex 
kotlin.String  toUri 
kotlin.String  	uppercase 
kotlin.String  message kotlin.Throwable  
Collection kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  contains kotlin.collections  
contentEquals kotlin.collections  
dropLastWhile kotlin.collections  filter kotlin.collections  filterIsInstance kotlin.collections  forEach kotlin.collections  get kotlin.collections  	hashMapOf kotlin.collections  indices kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  last kotlin.collections  
mapNotNull kotlin.collections  
mutableListOf kotlin.collections  orEmpty kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  
dropLastWhile kotlin.collections.List  filter kotlin.collections.List  get kotlin.collections.List  
isNotEmpty kotlin.collections.List  last kotlin.collections.List  let kotlin.collections.List  size kotlin.collections.List  toTypedArray kotlin.collections.List  add kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  endsWith 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  	JvmStatic 
kotlin.jvm  Throws 
kotlin.jvm  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  last 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  filterIsInstance kotlin.sequences  forEach kotlin.sequences  last kotlin.sequences  
mapNotNull kotlin.sequences  orEmpty kotlin.sequences  
MatchGroup kotlin.text  Regex kotlin.text  contains kotlin.text  
contentEquals kotlin.text  
dropLastWhile kotlin.text  endsWith kotlin.text  equals kotlin.text  filter kotlin.text  forEach kotlin.text  get kotlin.text  indices kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  last kotlin.text  
mapNotNull kotlin.text  matches kotlin.text  orEmpty kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  substringAfter kotlin.text  substringAfterLast kotlin.text  toRegex kotlin.text  toString kotlin.text  	uppercase kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  DocumentsContract !kotlinx.coroutines.CoroutineScope  addFile !kotlinx.coroutines.CoroutineScope  filterIsInstance !kotlinx.coroutines.CoroutineScope  finishWithError !kotlinx.coroutines.CoroutineScope  finishWithSuccess !kotlinx.coroutines.CoroutineScope  getFullPathFromTreeUri !kotlinx.coroutines.CoroutineScope  getSelectedItems !kotlinx.coroutines.CoroutineScope  handleFileResult !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  
processUri !kotlinx.coroutines.CoroutineScope  until !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Tika org.apache.tika  detect org.apache.tika.Tika  detector org.apache.tika.Tika  detect org.apache.tika.detect.Detector  TikaInputStream org.apache.tika.io  get "org.apache.tika.io.TikaInputStream  Metadata org.apache.tika.metadata  TikaCoreProperties org.apache.tika.metadata  set !org.apache.tika.metadata.Metadata  RESOURCE_NAME_KEY +org.apache.tika.metadata.TikaCoreProperties  toString org.apache.tika.mime.MediaType                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      