  Manifest android  RECORD_AUDIO android.Manifest.permission  SuppressLint android.annotation  Activity android.app  BroadcastReceiver android.content  Context android.content  Intent android.content  IntentFilter android.content  Array !android.content.BroadcastReceiver  AudioDeviceCallback !android.content.BroadcastReceiver  AudioDeviceInfo !android.content.BroadcastReceiver  AudioManager !android.content.BroadcastReceiver  BluetoothScoListener !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  DeviceUtils !android.content.BroadcastReceiver  HashSet !android.content.BroadcastReceiver  IntentFilter !android.content.BroadcastReceiver  any !android.content.BroadcastReceiver  asList !android.content.BroadcastReceiver  devices !android.content.BroadcastReceiver  
filterSources !android.content.BroadcastReceiver  
isNotEmpty !android.content.BroadcastReceiver  startBluetooth !android.content.BroadcastReceiver  
stopBluetooth !android.content.BroadcastReceiver  toSet !android.content.BroadcastReceiver  
AUDIO_SERVICE android.content.Context  getSystemService android.content.Context  registerReceiver android.content.Context  unregisterReceiver android.content.Context  getIntExtra android.content.Intent  	addAction android.content.IntentFilter  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  AudioDeviceCallback 
android.media  AudioDeviceInfo 
android.media  AudioFormat 
android.media  AudioManager 
android.media  AudioRecord 
android.media  
MediaCodec 
android.media  MediaCodecInfo 
android.media  MediaCodecList 
android.media  MediaFormat 
android.media  
MediaMuxer 
android.media  
MediaRecorder 
android.media  AudioDeviceInfo !android.media.AudioDeviceCallback  DeviceUtils !android.media.AudioDeviceCallback  any !android.media.AudioDeviceCallback  asList !android.media.AudioDeviceCallback  devices !android.media.AudioDeviceCallback  
filterSources !android.media.AudioDeviceCallback  startBluetooth !android.media.AudioDeviceCallback  
stopBluetooth !android.media.AudioDeviceCallback  toSet !android.media.AudioDeviceCallback  
TYPE_AUX_LINE android.media.AudioDeviceInfo  TYPE_BLE_BROADCAST android.media.AudioDeviceInfo  TYPE_BLE_HEADSET android.media.AudioDeviceInfo  TYPE_BLE_SPEAKER android.media.AudioDeviceInfo  TYPE_BLUETOOTH_A2DP android.media.AudioDeviceInfo  TYPE_BLUETOOTH_SCO android.media.AudioDeviceInfo  TYPE_BUILTIN_EARPIECE android.media.AudioDeviceInfo  TYPE_BUILTIN_MIC android.media.AudioDeviceInfo  TYPE_BUILTIN_SPEAKER android.media.AudioDeviceInfo  TYPE_BUILTIN_SPEAKER_SAFE android.media.AudioDeviceInfo  TYPE_BUS android.media.AudioDeviceInfo  	TYPE_DOCK android.media.AudioDeviceInfo  TYPE_FM android.media.AudioDeviceInfo  
TYPE_FM_TUNER android.media.AudioDeviceInfo  	TYPE_HDMI android.media.AudioDeviceInfo  
TYPE_HDMI_ARC android.media.AudioDeviceInfo  TYPE_HDMI_EARC android.media.AudioDeviceInfo  TYPE_HEARING_AID android.media.AudioDeviceInfo  TYPE_IP android.media.AudioDeviceInfo  TYPE_LINE_ANALOG android.media.AudioDeviceInfo  TYPE_LINE_DIGITAL android.media.AudioDeviceInfo  TYPE_REMOTE_SUBMIX android.media.AudioDeviceInfo  TYPE_TELEPHONY android.media.AudioDeviceInfo  
TYPE_TV_TUNER android.media.AudioDeviceInfo  TYPE_UNKNOWN android.media.AudioDeviceInfo  TYPE_USB_ACCESSORY android.media.AudioDeviceInfo  TYPE_USB_DEVICE android.media.AudioDeviceInfo  TYPE_USB_HEADSET android.media.AudioDeviceInfo  TYPE_WIRED_HEADPHONES android.media.AudioDeviceInfo  TYPE_WIRED_HEADSET android.media.AudioDeviceInfo  address android.media.AudioDeviceInfo  id android.media.AudioDeviceInfo  isSource android.media.AudioDeviceInfo  productName android.media.AudioDeviceInfo  type android.media.AudioDeviceInfo  CHANNEL_IN_MONO android.media.AudioFormat  CHANNEL_IN_STEREO android.media.AudioFormat  ENCODING_PCM_16BIT android.media.AudioFormat  ACTION_SCO_AUDIO_STATE_UPDATED android.media.AudioManager  ADJUST_MUTE android.media.AudioManager  
ADJUST_UNMUTE android.media.AudioManager  EXTRA_SCO_AUDIO_STATE android.media.AudioManager  GET_DEVICES_INPUTS android.media.AudioManager  MODE_CALL_REDIRECT android.media.AudioManager  MODE_CALL_SCREENING android.media.AudioManager  MODE_COMMUNICATION_REDIRECT android.media.AudioManager  MODE_IN_CALL android.media.AudioManager  MODE_IN_COMMUNICATION android.media.AudioManager  MODE_NORMAL android.media.AudioManager  
MODE_RINGTONE android.media.AudioManager  SCO_AUDIO_STATE_CONNECTED android.media.AudioManager  SCO_AUDIO_STATE_DISCONNECTED android.media.AudioManager  STREAM_ALARM android.media.AudioManager  STREAM_DTMF android.media.AudioManager  STREAM_MUSIC android.media.AudioManager  STREAM_NOTIFICATION android.media.AudioManager  STREAM_RING android.media.AudioManager  
STREAM_SYSTEM android.media.AudioManager  STREAM_VOICE_CALL android.media.AudioManager  
getDevices android.media.AudioManager  getStreamVolume android.media.AudioManager  isBluetoothScoAvailableOffCall android.media.AudioManager  isBluetoothScoOn android.media.AudioManager  isSpeakerphoneOn android.media.AudioManager  mode android.media.AudioManager  registerAudioDeviceCallback android.media.AudioManager  setSpeakerphoneOn android.media.AudioManager  setStreamVolume android.media.AudioManager  startBluetoothSco android.media.AudioManager  stopBluetoothSco android.media.AudioManager  unregisterAudioDeviceCallback android.media.AudioManager  ERROR android.media.AudioRecord  ERROR_BAD_VALUE android.media.AudioRecord  ERROR_DEAD_OBJECT android.media.AudioRecord  ERROR_INVALID_OPERATION android.media.AudioRecord  RECORDSTATE_RECORDING android.media.AudioRecord  STATE_INITIALIZED android.media.AudioRecord  audioSessionId android.media.AudioRecord  getMinBufferSize android.media.AudioRecord  read android.media.AudioRecord  recordingState android.media.AudioRecord  release android.media.AudioRecord  setPreferredDevice android.media.AudioRecord  startRecording android.media.AudioRecord  state android.media.AudioRecord  stop android.media.AudioRecord  BUFFER_FLAG_END_OF_STREAM android.media.MediaCodec  
BufferInfo android.media.MediaCodec  CONFIGURE_FLAG_ENCODE android.media.MediaCodec  Callback android.media.MediaCodec  CodecException android.media.MediaCodec  	configure android.media.MediaCodec  createByCodecName android.media.MediaCodec  getInputBuffer android.media.MediaCodec  getOutputBuffer android.media.MediaCodec  queueInputBuffer android.media.MediaCodec  release android.media.MediaCodec  releaseOutputBuffer android.media.MediaCodec  setCallback android.media.MediaCodec  start android.media.MediaCodec  stop android.media.MediaCodec  flags #android.media.MediaCodec.BufferInfo  offset #android.media.MediaCodec.BufferInfo  presentationTimeUs #android.media.MediaCodec.BufferInfo  size #android.media.MediaCodec.BufferInfo  Log !android.media.MediaCodec.Callback  
mContainer !android.media.MediaCodec.Callback  mContainerTrack !android.media.MediaCodec.Callback  mInputBufferIndex !android.media.MediaCodec.Callback  onError !android.media.MediaCodec.Callback  processInputBuffer !android.media.MediaCodec.Callback  processOutputBuffer !android.media.MediaCodec.Callback  CodecCapabilities android.media.MediaCodecInfo  getCapabilitiesForType android.media.MediaCodecInfo  	isEncoder android.media.MediaCodecInfo  name android.media.MediaCodecInfo  supportedTypes android.media.MediaCodecInfo  bitrateRange .android.media.MediaCodecInfo.AudioCapabilities  maxInputChannelCount .android.media.MediaCodecInfo.AudioCapabilities  supportedSampleRates .android.media.MediaCodecInfo.AudioCapabilities  audioCapabilities .android.media.MediaCodecInfo.CodecCapabilities  isFormatSupported .android.media.MediaCodecInfo.CodecCapabilities  AACObjectELD .android.media.MediaCodecInfo.CodecProfileLevel  AACObjectHE .android.media.MediaCodecInfo.CodecProfileLevel  AACObjectLC .android.media.MediaCodecInfo.CodecProfileLevel  REGULAR_CODECS android.media.MediaCodecList  
codecInfos android.media.MediaCodecList  AudioEncoder android.media.MediaFormat  KEY_AAC_PROFILE android.media.MediaFormat  KEY_BIT_RATE android.media.MediaFormat  KEY_CHANNEL_COUNT android.media.MediaFormat  KEY_CHANNEL_MASK android.media.MediaFormat  KEY_FLAC_COMPRESSION_LEVEL android.media.MediaFormat  KEY_MIME android.media.MediaFormat  KEY_SAMPLE_RATE android.media.MediaFormat  KEY_X_FRAME_SIZE_IN_BYTES android.media.MediaFormat  MIMETYPE_AUDIO_AAC android.media.MediaFormat  MIMETYPE_AUDIO_AMR_NB android.media.MediaFormat  MIMETYPE_AUDIO_AMR_WB android.media.MediaFormat  MIMETYPE_AUDIO_FLAC android.media.MediaFormat  MIMETYPE_AUDIO_OPUS android.media.MediaFormat  MIMETYPE_AUDIO_RAW android.media.MediaFormat  MediaCodecInfo android.media.MediaFormat  MediaFormat android.media.MediaFormat  apply android.media.MediaFormat  bitRates android.media.MediaFormat  	frameSize android.media.MediaFormat  
getInteger android.media.MediaFormat  
mimeTypeAudio android.media.MediaFormat  nearestValue android.media.MediaFormat  sampleRates android.media.MediaFormat  
setInteger android.media.MediaFormat  	setString android.media.MediaFormat  addTrack android.media.MediaMuxer  release android.media.MediaMuxer  start android.media.MediaMuxer  stop android.media.MediaMuxer  writeSampleData android.media.MediaMuxer  MUXER_OUTPUT_3GPP %android.media.MediaMuxer.OutputFormat  MUXER_OUTPUT_MPEG_4 %android.media.MediaMuxer.OutputFormat  MUXER_OUTPUT_OGG %android.media.MediaMuxer.OutputFormat  maxAmplitude android.media.MediaRecorder  pause android.media.MediaRecorder  prepare android.media.MediaRecorder  release android.media.MediaRecorder  reset android.media.MediaRecorder  resume android.media.MediaRecorder  setAudioChannels android.media.MediaRecorder  setAudioEncoder android.media.MediaRecorder  setAudioEncodingBitRate android.media.MediaRecorder  setAudioSamplingRate android.media.MediaRecorder  setAudioSource android.media.MediaRecorder  
setOutputFile android.media.MediaRecorder  setOutputFormat android.media.MediaRecorder  start android.media.MediaRecorder  stop android.media.MediaRecorder  AAC (android.media.MediaRecorder.AudioEncoder  AAC_ELD (android.media.MediaRecorder.AudioEncoder  AMR_NB (android.media.MediaRecorder.AudioEncoder  AMR_WB (android.media.MediaRecorder.AudioEncoder  HE_AAC (android.media.MediaRecorder.AudioEncoder  OPUS (android.media.MediaRecorder.AudioEncoder  	CAMCORDER 'android.media.MediaRecorder.AudioSource  DEFAULT 'android.media.MediaRecorder.AudioSource  MIC 'android.media.MediaRecorder.AudioSource  
REMOTE_SUBMIX 'android.media.MediaRecorder.AudioSource  UNPROCESSED 'android.media.MediaRecorder.AudioSource  
VOICE_CALL 'android.media.MediaRecorder.AudioSource  VOICE_COMMUNICATION 'android.media.MediaRecorder.AudioSource  VOICE_DOWNLINK 'android.media.MediaRecorder.AudioSource  VOICE_PERFORMANCE 'android.media.MediaRecorder.AudioSource  VOICE_RECOGNITION 'android.media.MediaRecorder.AudioSource  VOICE_UPLINK 'android.media.MediaRecorder.AudioSource  DEFAULT (android.media.MediaRecorder.OutputFormat  MPEG_4 (android.media.MediaRecorder.OutputFormat  OGG (android.media.MediaRecorder.OutputFormat  	THREE_GPP (android.media.MediaRecorder.OutputFormat  AcousticEchoCanceler android.media.audiofx  AutomaticGainControl android.media.audiofx  NoiseSuppressor android.media.audiofx  create *android.media.audiofx.AcousticEchoCanceler  enabled *android.media.audiofx.AcousticEchoCanceler  isAvailable *android.media.audiofx.AcousticEchoCanceler  release *android.media.audiofx.AcousticEchoCanceler  enabled !android.media.audiofx.AudioEffect  release !android.media.audiofx.AudioEffect  create *android.media.audiofx.AutomaticGainControl  enabled *android.media.audiofx.AutomaticGainControl  isAvailable *android.media.audiofx.AutomaticGainControl  release *android.media.audiofx.AutomaticGainControl  create %android.media.audiofx.NoiseSuppressor  enabled %android.media.audiofx.NoiseSuppressor  isAvailable %android.media.audiofx.NoiseSuppressor  release %android.media.audiofx.NoiseSuppressor  Build 
android.os  Handler 
android.os  
HandlerThread 
android.os  Looper 
android.os  Message 
android.os  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  Callback android.os.Handler  
obtainMessage android.os.Handler  post android.os.Handler  
AtomicBoolean android.os.HandlerThread  	Exception android.os.HandlerThread  Handler android.os.HandlerThread  
LinkedList android.os.HandlerThread  Log android.os.HandlerThread  MSG_ENCODE_INPUT android.os.HandlerThread  MSG_INIT android.os.HandlerThread  MSG_STOP android.os.HandlerThread  
MediaCodec android.os.HandlerThread  MediaFormat android.os.HandlerThread  Sample android.os.HandlerThread  	Semaphore android.os.HandlerThread  	divAssign android.os.HandlerThread  looper android.os.HandlerThread  
mContainer android.os.HandlerThread  mContainerTrack android.os.HandlerThread  mInputBufferIndex android.os.HandlerThread  min android.os.HandlerThread  onError android.os.HandlerThread  
plusAssign android.os.HandlerThread  processInputBuffer android.os.HandlerThread  processOutputBuffer android.os.HandlerThread  
quitSafely android.os.HandlerThread  timesAssign android.os.HandlerThread  
getMainLooper android.os.Looper  obj android.os.Message  sendToTarget android.os.Message  what android.os.Message  Os android.system  OsConstants android.system  	ftruncate android.system.Os  lseek android.system.Os  read android.system.Os  write android.system.Os  SEEK_CUR android.system.OsConstants  SEEK_SET android.system.OsConstants  Log android.util  Range android.util  d android.util.Log  e android.util.Log  w android.util.Log  lower android.util.Range  upper android.util.Range  RequiresApi androidx.annotation  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  checkSelfPermission #androidx.core.content.ContextCompat  
ActivityAware com.llfbandit.record  ActivityPluginBinding com.llfbandit.record  
FlutterPlugin com.llfbandit.record  FlutterPluginBinding com.llfbandit.record  MESSAGES_CHANNEL com.llfbandit.record  MethodCallHandlerImpl com.llfbandit.record  
MethodChannel com.llfbandit.record  PermissionManager com.llfbandit.record  RecordPlugin com.llfbandit.record  Utils com.llfbandit.record  ActivityPluginBinding !com.llfbandit.record.RecordPlugin  FlutterPluginBinding !com.llfbandit.record.RecordPlugin  MESSAGES_CHANNEL !com.llfbandit.record.RecordPlugin  MethodCallHandlerImpl !com.llfbandit.record.RecordPlugin  
MethodChannel !com.llfbandit.record.RecordPlugin  PermissionManager !com.llfbandit.record.RecordPlugin  activityBinding !com.llfbandit.record.RecordPlugin  callHandler !com.llfbandit.record.RecordPlugin  
methodChannel !com.llfbandit.record.RecordPlugin  onAttachedToActivity !com.llfbandit.record.RecordPlugin  onDetachedFromActivity !com.llfbandit.record.RecordPlugin  permissionManager !com.llfbandit.record.RecordPlugin  startPlugin !com.llfbandit.record.RecordPlugin  
stopPlugin !com.llfbandit.record.RecordPlugin  MESSAGES_CHANNEL +com.llfbandit.record.RecordPlugin.Companion  MethodCallHandlerImpl +com.llfbandit.record.RecordPlugin.Companion  
MethodChannel +com.llfbandit.record.RecordPlugin.Companion  PermissionManager +com.llfbandit.record.RecordPlugin.Companion  
deleteFile com.llfbandit.record.Utils  firstNonNull com.llfbandit.record.Utils  Any com.llfbandit.record.methodcall  AudioDeviceInfo com.llfbandit.record.methodcall  AudioFormats com.llfbandit.record.methodcall  AudioManager com.llfbandit.record.methodcall  
AudioRecorder com.llfbandit.record.methodcall  BinaryMessenger com.llfbandit.record.methodcall  BluetoothReceiver com.llfbandit.record.methodcall  BluetoothScoListener com.llfbandit.record.methodcall  Boolean com.llfbandit.record.methodcall  Build com.llfbandit.record.methodcall  ConcurrentHashMap com.llfbandit.record.methodcall  Context com.llfbandit.record.methodcall  DeviceUtils com.llfbandit.record.methodcall  EVENTS_RECORD_CHANNEL com.llfbandit.record.methodcall  EVENTS_STATE_CHANNEL com.llfbandit.record.methodcall  EventChannel com.llfbandit.record.methodcall  	Exception com.llfbandit.record.methodcall  HashMap com.llfbandit.record.methodcall  IOException com.llfbandit.record.methodcall  	IRecorder com.llfbandit.record.methodcall  Int com.llfbandit.record.methodcall  Map com.llfbandit.record.methodcall  
MediaRecorder com.llfbandit.record.methodcall  
MethodCall com.llfbandit.record.methodcall  MethodCallHandler com.llfbandit.record.methodcall  MethodCallHandlerImpl com.llfbandit.record.methodcall  
MethodChannel com.llfbandit.record.methodcall  
MutableMap com.llfbandit.record.methodcall  Objects com.llfbandit.record.methodcall  PermissionManager com.llfbandit.record.methodcall  RecordConfig com.llfbandit.record.methodcall  RecorderRecordStreamHandler com.llfbandit.record.methodcall  RecorderStateStreamHandler com.llfbandit.record.methodcall  RecorderWrapper com.llfbandit.record.methodcall  String com.llfbandit.record.methodcall  Utils com.llfbandit.record.methodcall  deviceInfoFromMap com.llfbandit.record.methodcall  get com.llfbandit.record.methodcall  getMimeType com.llfbandit.record.methodcall  isEncoderSupported com.llfbandit.record.methodcall  
isNullOrEmpty com.llfbandit.record.methodcall  listInputDevicesAsMap com.llfbandit.record.methodcall  set com.llfbandit.record.methodcall  AudioFormats 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  AudioManager 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  Build 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  ConcurrentHashMap 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  DeviceUtils 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  
MediaRecorder 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  Objects 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  RecordConfig 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  RecorderWrapper 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  Utils 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  
appContext 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  createRecorder 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  deviceInfoFromMap 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  dispose 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  disposeRecorder 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  get 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  getMimeType 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  getRecordConfig 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  isEncoderSupported 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  
isNullOrEmpty 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  listInputDevicesAsMap 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  	messenger 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  permissionManager 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  	recorders 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  set 5com.llfbandit.record.methodcall.MethodCallHandlerImpl  Result -com.llfbandit.record.methodcall.MethodChannel  Any /com.llfbandit.record.methodcall.RecorderWrapper  AudioDeviceInfo /com.llfbandit.record.methodcall.RecorderWrapper  
AudioRecorder /com.llfbandit.record.methodcall.RecorderWrapper  BinaryMessenger /com.llfbandit.record.methodcall.RecorderWrapper  BluetoothReceiver /com.llfbandit.record.methodcall.RecorderWrapper  Context /com.llfbandit.record.methodcall.RecorderWrapper  EVENTS_RECORD_CHANNEL /com.llfbandit.record.methodcall.RecorderWrapper  EVENTS_STATE_CHANNEL /com.llfbandit.record.methodcall.RecorderWrapper  EventChannel /com.llfbandit.record.methodcall.RecorderWrapper  	Exception /com.llfbandit.record.methodcall.RecorderWrapper  HashMap /com.llfbandit.record.methodcall.RecorderWrapper  	IRecorder /com.llfbandit.record.methodcall.RecorderWrapper  
MediaRecorder /com.llfbandit.record.methodcall.RecorderWrapper  
MethodChannel /com.llfbandit.record.methodcall.RecorderWrapper  
MutableMap /com.llfbandit.record.methodcall.RecorderWrapper  RecordConfig /com.llfbandit.record.methodcall.RecorderWrapper  RecorderRecordStreamHandler /com.llfbandit.record.methodcall.RecorderWrapper  RecorderStateStreamHandler /com.llfbandit.record.methodcall.RecorderWrapper  String /com.llfbandit.record.methodcall.RecorderWrapper  bluetoothReceiver /com.llfbandit.record.methodcall.RecorderWrapper  cancel /com.llfbandit.record.methodcall.RecorderWrapper  context /com.llfbandit.record.methodcall.RecorderWrapper  createRecorder /com.llfbandit.record.methodcall.RecorderWrapper  dispose /com.llfbandit.record.methodcall.RecorderWrapper  eventChannel /com.llfbandit.record.methodcall.RecorderWrapper  eventRecordChannel /com.llfbandit.record.methodcall.RecorderWrapper  getAmplitude /com.llfbandit.record.methodcall.RecorderWrapper  isPaused /com.llfbandit.record.methodcall.RecorderWrapper  isRecording /com.llfbandit.record.methodcall.RecorderWrapper  maybeStartBluetooth /com.llfbandit.record.methodcall.RecorderWrapper  maybeStopBluetooth /com.llfbandit.record.methodcall.RecorderWrapper  pause /com.llfbandit.record.methodcall.RecorderWrapper  recorder /com.llfbandit.record.methodcall.RecorderWrapper  recorderRecordStreamHandler /com.llfbandit.record.methodcall.RecorderWrapper  recorderStateStreamHandler /com.llfbandit.record.methodcall.RecorderWrapper  resume /com.llfbandit.record.methodcall.RecorderWrapper  set /com.llfbandit.record.methodcall.RecorderWrapper  start /com.llfbandit.record.methodcall.RecorderWrapper  startRecording /com.llfbandit.record.methodcall.RecorderWrapper  startRecordingToFile /com.llfbandit.record.methodcall.RecorderWrapper  startRecordingToStream /com.llfbandit.record.methodcall.RecorderWrapper  stop /com.llfbandit.record.methodcall.RecorderWrapper  AudioDeviceInfo 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  
AudioRecorder 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  BluetoothReceiver 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  EVENTS_RECORD_CHANNEL 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  EVENTS_STATE_CHANNEL 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  EventChannel 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  	Exception 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  HashMap 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  
MediaRecorder 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  RecorderRecordStreamHandler 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  RecorderStateStreamHandler 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  set 9com.llfbandit.record.methodcall.RecorderWrapper.Companion  Result =com.llfbandit.record.methodcall.RecorderWrapper.MethodChannel  Activity com.llfbandit.record.permission  ActivityCompat com.llfbandit.record.permission  Array com.llfbandit.record.permission  Boolean com.llfbandit.record.permission  Int com.llfbandit.record.permission  IntArray com.llfbandit.record.permission  Manifest com.llfbandit.record.permission  PackageManager com.llfbandit.record.permission  PermissionManager com.llfbandit.record.permission  PermissionResultCallback com.llfbandit.record.permission  RECORD_AUDIO_REQUEST_CODE com.llfbandit.record.permission   RequestPermissionsResultListener com.llfbandit.record.permission  String com.llfbandit.record.permission  arrayOf com.llfbandit.record.permission  
isNotEmpty com.llfbandit.record.permission  Activity 1com.llfbandit.record.permission.PermissionManager  ActivityCompat 1com.llfbandit.record.permission.PermissionManager  Array 1com.llfbandit.record.permission.PermissionManager  Boolean 1com.llfbandit.record.permission.PermissionManager  Int 1com.llfbandit.record.permission.PermissionManager  IntArray 1com.llfbandit.record.permission.PermissionManager  Manifest 1com.llfbandit.record.permission.PermissionManager  PackageManager 1com.llfbandit.record.permission.PermissionManager  PermissionResultCallback 1com.llfbandit.record.permission.PermissionManager  RECORD_AUDIO_REQUEST_CODE 1com.llfbandit.record.permission.PermissionManager  String 1com.llfbandit.record.permission.PermissionManager  activity 1com.llfbandit.record.permission.PermissionManager  arrayOf 1com.llfbandit.record.permission.PermissionManager  
hasPermission 1com.llfbandit.record.permission.PermissionManager  
isNotEmpty 1com.llfbandit.record.permission.PermissionManager  isPermissionGranted 1com.llfbandit.record.permission.PermissionManager  resultCallback 1com.llfbandit.record.permission.PermissionManager  setActivity 1com.llfbandit.record.permission.PermissionManager  ActivityCompat ;com.llfbandit.record.permission.PermissionManager.Companion  Manifest ;com.llfbandit.record.permission.PermissionManager.Companion  PackageManager ;com.llfbandit.record.permission.PermissionManager.Companion  RECORD_AUDIO_REQUEST_CODE ;com.llfbandit.record.permission.PermissionManager.Companion  arrayOf ;com.llfbandit.record.permission.PermissionManager.Companion  
isNotEmpty ;com.llfbandit.record.permission.PermissionManager.Companion  <SAM-CONSTRUCTOR> 8com.llfbandit.record.permission.PermissionResultCallback  onResult 8com.llfbandit.record.permission.PermissionResultCallback  AcousticEchoCanceler com.llfbandit.record.record  AudioDeviceInfo com.llfbandit.record.record  AudioEncoder com.llfbandit.record.record  AudioFormat com.llfbandit.record.record  AudioManager com.llfbandit.record.record  AudioRecord com.llfbandit.record.record  AutomaticGainControl com.llfbandit.record.record  Boolean com.llfbandit.record.record  	ByteArray com.llfbandit.record.record  
ByteBuffer com.llfbandit.record.record  	ByteOrder com.llfbandit.record.record  Double com.llfbandit.record.record  	Exception com.llfbandit.record.record  IllegalArgumentException com.llfbandit.record.record  IllegalStateException com.llfbandit.record.record  Int com.llfbandit.record.record  Log com.llfbandit.record.record  MediaFormat com.llfbandit.record.record  
MediaRecorder com.llfbandit.record.record  NoiseSuppressor com.llfbandit.record.record  	PCMReader com.llfbandit.record.record  RecordConfig com.llfbandit.record.record  RecordState com.llfbandit.record.record  
ShortArray com.llfbandit.record.record  String com.llfbandit.record.record  
StringBuilder com.llfbandit.record.record  SuppressLint com.llfbandit.record.record  TAG com.llfbandit.record.record  Throws com.llfbandit.record.record  abs com.llfbandit.record.record  
appendLine com.llfbandit.record.record  
coerceAtLeast com.llfbandit.record.record  coerceAtMost com.llfbandit.record.record  java com.llfbandit.record.record  log10 com.llfbandit.record.record  	Companion (com.llfbandit.record.record.AudioEncoder  aacEld (com.llfbandit.record.record.AudioEncoder  aacHe (com.llfbandit.record.record.AudioEncoder  aacLc (com.llfbandit.record.record.AudioEncoder  amrNb (com.llfbandit.record.record.AudioEncoder  amrWb (com.llfbandit.record.record.AudioEncoder  flac (com.llfbandit.record.record.AudioEncoder  opus (com.llfbandit.record.record.AudioEncoder  	pcm16bits (com.llfbandit.record.record.AudioEncoder  wav (com.llfbandit.record.record.AudioEncoder  aacEld 2com.llfbandit.record.record.AudioEncoder.Companion  aacHe 2com.llfbandit.record.record.AudioEncoder.Companion  aacLc 2com.llfbandit.record.record.AudioEncoder.Companion  amrNb 2com.llfbandit.record.record.AudioEncoder.Companion  amrWb 2com.llfbandit.record.record.AudioEncoder.Companion  flac 2com.llfbandit.record.record.AudioEncoder.Companion  opus 2com.llfbandit.record.record.AudioEncoder.Companion  	pcm16bits 2com.llfbandit.record.record.AudioEncoder.Companion  wav 2com.llfbandit.record.record.AudioEncoder.Companion  AcousticEchoCanceler %com.llfbandit.record.record.PCMReader  AudioFormat %com.llfbandit.record.record.PCMReader  AudioRecord %com.llfbandit.record.record.PCMReader  AutomaticGainControl %com.llfbandit.record.record.PCMReader  	ByteArray %com.llfbandit.record.record.PCMReader  
ByteBuffer %com.llfbandit.record.record.PCMReader  	ByteOrder %com.llfbandit.record.record.PCMReader  	Companion %com.llfbandit.record.record.PCMReader  Double %com.llfbandit.record.record.PCMReader  	Exception %com.llfbandit.record.record.PCMReader  IllegalArgumentException %com.llfbandit.record.record.PCMReader  IllegalStateException %com.llfbandit.record.record.PCMReader  Int %com.llfbandit.record.record.PCMReader  Log %com.llfbandit.record.record.PCMReader  MediaFormat %com.llfbandit.record.record.PCMReader  NoiseSuppressor %com.llfbandit.record.record.PCMReader  	PCMReader %com.llfbandit.record.record.PCMReader  RecordConfig %com.llfbandit.record.record.PCMReader  
ShortArray %com.llfbandit.record.record.PCMReader  String %com.llfbandit.record.record.PCMReader  
StringBuilder %com.llfbandit.record.record.PCMReader  SuppressLint %com.llfbandit.record.record.PCMReader  TAG %com.llfbandit.record.record.PCMReader  Throws %com.llfbandit.record.record.PCMReader  abs %com.llfbandit.record.record.PCMReader  acousticEchoCanceler %com.llfbandit.record.record.PCMReader  	amplitude %com.llfbandit.record.record.PCMReader  
appendLine %com.llfbandit.record.record.PCMReader  audioFormat %com.llfbandit.record.record.PCMReader  automaticGainControl %com.llfbandit.record.record.PCMReader  
bufferSize %com.llfbandit.record.record.PCMReader  channels %com.llfbandit.record.record.PCMReader  config %com.llfbandit.record.record.PCMReader  createReader %com.llfbandit.record.record.PCMReader  enableAutomaticGainControl %com.llfbandit.record.record.PCMReader  enableEchoSuppressor %com.llfbandit.record.record.PCMReader  enableNoiseSuppressor %com.llfbandit.record.record.PCMReader  getAmplitude %com.llfbandit.record.record.PCMReader  getMinBufferSize %com.llfbandit.record.record.PCMReader  getReadFailureReason %com.llfbandit.record.record.PCMReader  java %com.llfbandit.record.record.PCMReader  log10 %com.llfbandit.record.record.PCMReader  mediaFormat %com.llfbandit.record.record.PCMReader  noiseSuppressor %com.llfbandit.record.record.PCMReader  read %com.llfbandit.record.record.PCMReader  reader %com.llfbandit.record.record.PCMReader  release %com.llfbandit.record.record.PCMReader  start %com.llfbandit.record.record.PCMReader  stop %com.llfbandit.record.record.PCMReader  AcousticEchoCanceler /com.llfbandit.record.record.PCMReader.Companion  AudioFormat /com.llfbandit.record.record.PCMReader.Companion  AudioRecord /com.llfbandit.record.record.PCMReader.Companion  AutomaticGainControl /com.llfbandit.record.record.PCMReader.Companion  	ByteArray /com.llfbandit.record.record.PCMReader.Companion  
ByteBuffer /com.llfbandit.record.record.PCMReader.Companion  	ByteOrder /com.llfbandit.record.record.PCMReader.Companion  	Exception /com.llfbandit.record.record.PCMReader.Companion  Log /com.llfbandit.record.record.PCMReader.Companion  MediaFormat /com.llfbandit.record.record.PCMReader.Companion  NoiseSuppressor /com.llfbandit.record.record.PCMReader.Companion  	PCMReader /com.llfbandit.record.record.PCMReader.Companion  
ShortArray /com.llfbandit.record.record.PCMReader.Companion  
StringBuilder /com.llfbandit.record.record.PCMReader.Companion  TAG /com.llfbandit.record.record.PCMReader.Companion  abs /com.llfbandit.record.record.PCMReader.Companion  
appendLine /com.llfbandit.record.record.PCMReader.Companion  java /com.llfbandit.record.record.PCMReader.Companion  log10 /com.llfbandit.record.record.PCMReader.Companion  audioManagerMode (com.llfbandit.record.record.RecordConfig  audioSource (com.llfbandit.record.record.RecordConfig  autoGain (com.llfbandit.record.record.RecordConfig  bitRate (com.llfbandit.record.record.RecordConfig  
coerceAtLeast (com.llfbandit.record.record.RecordConfig  coerceAtMost (com.llfbandit.record.record.RecordConfig  device (com.llfbandit.record.record.RecordConfig  
echoCancel (com.llfbandit.record.record.RecordConfig  encoder (com.llfbandit.record.record.RecordConfig  manageBluetooth (com.llfbandit.record.record.RecordConfig  	muteAudio (com.llfbandit.record.record.RecordConfig  
noiseSuppress (com.llfbandit.record.record.RecordConfig  numChannels (com.llfbandit.record.record.RecordConfig  path (com.llfbandit.record.record.RecordConfig  
sampleRate (com.llfbandit.record.record.RecordConfig  speakerphone (com.llfbandit.record.record.RecordConfig  	useLegacy (com.llfbandit.record.record.RecordConfig  PAUSE 'com.llfbandit.record.record.RecordState  RECORD 'com.llfbandit.record.record.RecordState  STOP 'com.llfbandit.record.record.RecordState  id 'com.llfbandit.record.record.RecordState  Array %com.llfbandit.record.record.bluetooth  AudioDeviceCallback %com.llfbandit.record.record.bluetooth  AudioDeviceInfo %com.llfbandit.record.record.bluetooth  AudioManager %com.llfbandit.record.record.bluetooth  BluetoothReceiver %com.llfbandit.record.record.bluetooth  BluetoothScoListener %com.llfbandit.record.record.bluetooth  Boolean %com.llfbandit.record.record.bluetooth  BroadcastReceiver %com.llfbandit.record.record.bluetooth  Context %com.llfbandit.record.record.bluetooth  DeviceUtils %com.llfbandit.record.record.bluetooth  HashSet %com.llfbandit.record.record.bluetooth  Intent %com.llfbandit.record.record.bluetooth  IntentFilter %com.llfbandit.record.record.bluetooth  Suppress %com.llfbandit.record.record.bluetooth  any %com.llfbandit.record.record.bluetooth  asList %com.llfbandit.record.record.bluetooth  devices %com.llfbandit.record.record.bluetooth  
filterSources %com.llfbandit.record.record.bluetooth  forEach %com.llfbandit.record.record.bluetooth  
isNotEmpty %com.llfbandit.record.record.bluetooth  startBluetooth %com.llfbandit.record.record.bluetooth  
stopBluetooth %com.llfbandit.record.record.bluetooth  toSet %com.llfbandit.record.record.bluetooth  AudioDeviceInfo 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  AudioManager 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  Context 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  DeviceUtils 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  HashSet 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  IntentFilter 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  addListener 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  any 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  asList 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  audioDeviceCallback 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  audioManager 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  context 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  devices 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  filter 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  
filterSources 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  hasListeners 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  
isNotEmpty 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  	listeners 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  mRegistered 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  register 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  removeListener 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  startBluetooth 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  
stopBluetooth 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  toSet 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  
unregister 7com.llfbandit.record.record.bluetooth.BluetoothReceiver  onBlScoConnected :com.llfbandit.record.record.bluetooth.BluetoothScoListener  onBlScoDisconnected :com.llfbandit.record.record.bluetooth.BluetoothScoListener  
AdtsContainer %com.llfbandit.record.record.container  
AtomicBoolean %com.llfbandit.record.record.container  Boolean %com.llfbandit.record.record.container  	ByteArray %com.llfbandit.record.record.container  
ByteBuffer %com.llfbandit.record.record.container  	ByteOrder %com.llfbandit.record.record.container  
DATA_MAGIC %com.llfbandit.record.record.container  ExperimentalUnsignedTypes %com.llfbandit.record.record.container  
FLAC_MAGIC %com.llfbandit.record.record.container  	FMT_MAGIC %com.llfbandit.record.record.container  
FlacContainer %com.llfbandit.record.record.container  HEADER_SIZE %com.llfbandit.record.record.container  IContainerWriter %com.llfbandit.record.record.container  IOException %com.llfbandit.record.record.container  IllegalStateException %com.llfbandit.record.record.container  Int %com.llfbandit.record.record.container  Long %com.llfbandit.record.record.container  
MediaCodec %com.llfbandit.record.record.container  MediaFormat %com.llfbandit.record.record.container  
MediaMuxer %com.llfbandit.record.record.container  MuxerContainer %com.llfbandit.record.record.container  NotImplementedError %com.llfbandit.record.record.container  OptIn %com.llfbandit.record.record.container  Os %com.llfbandit.record.record.container  OsConstants %com.llfbandit.record.record.container  Pair %com.llfbandit.record.record.container  
RIFF_MAGIC %com.llfbandit.record.record.container  RandomAccessFile %com.llfbandit.record.record.container  RawContainer %com.llfbandit.record.record.container  String %com.llfbandit.record.record.container  
UByteArray %com.llfbandit.record.record.container  
WAVE_MAGIC %com.llfbandit.record.record.container  
WaveContainer %com.llfbandit.record.record.container  apply %com.llfbandit.record.record.container  asByteArray %com.llfbandit.record.record.container  channelCount %com.llfbandit.record.record.container  check %com.llfbandit.record.record.container  	frameSize %com.llfbandit.record.record.container  indexOf %com.llfbandit.record.record.container  
intArrayOf %com.llfbandit.record.record.container  plus %com.llfbandit.record.record.container  
plusAssign %com.llfbandit.record.record.container  
sampleRate %com.llfbandit.record.record.container  toUByte %com.llfbandit.record.record.container  toULong %com.llfbandit.record.record.container  ubyteArrayOf %com.llfbandit.record.record.container  	ByteArray 3com.llfbandit.record.record.container.AdtsContainer  IllegalStateException 3com.llfbandit.record.record.container.AdtsContainer  
aacProfile 3com.llfbandit.record.record.container.AdtsContainer  addADTSFrame 3com.llfbandit.record.record.container.AdtsContainer  getFreqIndex 3com.llfbandit.record.record.container.AdtsContainer  indexOf 3com.llfbandit.record.record.container.AdtsContainer  
intArrayOf 3com.llfbandit.record.record.container.AdtsContainer  	isStarted 3com.llfbandit.record.record.container.AdtsContainer  numChannels 3com.llfbandit.record.record.container.AdtsContainer  plus 3com.llfbandit.record.record.container.AdtsContainer  
plusAssign 3com.llfbandit.record.record.container.AdtsContainer  
sampleRate 3com.llfbandit.record.record.container.AdtsContainer  sampleRates 3com.llfbandit.record.record.container.AdtsContainer  stop 3com.llfbandit.record.record.container.AdtsContainer  track 3com.llfbandit.record.record.container.AdtsContainer  
ByteBuffer 3com.llfbandit.record.record.container.FlacContainer  
FLAC_MAGIC 3com.llfbandit.record.record.container.FlacContainer  IOException 3com.llfbandit.record.record.container.FlacContainer  IllegalStateException 3com.llfbandit.record.record.container.FlacContainer  Int 3com.llfbandit.record.record.container.FlacContainer  
MediaCodec 3com.llfbandit.record.record.container.FlacContainer  MediaFormat 3com.llfbandit.record.record.container.FlacContainer  Os 3com.llfbandit.record.record.container.FlacContainer  OsConstants 3com.llfbandit.record.record.container.FlacContainer  String 3com.llfbandit.record.record.container.FlacContainer  
UByteArray 3com.llfbandit.record.record.container.FlacContainer  asByteArray 3com.llfbandit.record.record.container.FlacContainer  check 3com.llfbandit.record.record.container.FlacContainer  
createFile 3com.llfbandit.record.record.container.FlacContainer  file 3com.llfbandit.record.record.container.FlacContainer  	isStarted 3com.llfbandit.record.record.container.FlacContainer  lastPresentationTimeUs 3com.llfbandit.record.record.container.FlacContainer  setHeaderDuration 3com.llfbandit.record.record.container.FlacContainer  stop 3com.llfbandit.record.record.container.FlacContainer  toUByte 3com.llfbandit.record.record.container.FlacContainer  toULong 3com.llfbandit.record.record.container.FlacContainer  track 3com.llfbandit.record.record.container.FlacContainer  ubyteArrayOf 3com.llfbandit.record.record.container.FlacContainer  
ByteBuffer =com.llfbandit.record.record.container.FlacContainer.Companion  
FLAC_MAGIC =com.llfbandit.record.record.container.FlacContainer.Companion  IOException =com.llfbandit.record.record.container.FlacContainer.Companion  IllegalStateException =com.llfbandit.record.record.container.FlacContainer.Companion  
MediaCodec =com.llfbandit.record.record.container.FlacContainer.Companion  Os =com.llfbandit.record.record.container.FlacContainer.Companion  OsConstants =com.llfbandit.record.record.container.FlacContainer.Companion  
UByteArray =com.llfbandit.record.record.container.FlacContainer.Companion  asByteArray =com.llfbandit.record.record.container.FlacContainer.Companion  check =com.llfbandit.record.record.container.FlacContainer.Companion  toUByte =com.llfbandit.record.record.container.FlacContainer.Companion  toULong =com.llfbandit.record.record.container.FlacContainer.Companion  ubyteArrayOf =com.llfbandit.record.record.container.FlacContainer.Companion  
BufferInfo >com.llfbandit.record.record.container.FlacContainer.MediaCodec  NotImplementedError 6com.llfbandit.record.record.container.IContainerWriter  RandomAccessFile 6com.llfbandit.record.record.container.IContainerWriter  addTrack 6com.llfbandit.record.record.container.IContainerWriter  
createFile 6com.llfbandit.record.record.container.IContainerWriter  isStream 6com.llfbandit.record.record.container.IContainerWriter  release 6com.llfbandit.record.record.container.IContainerWriter  start 6com.llfbandit.record.record.container.IContainerWriter  stop 6com.llfbandit.record.record.container.IContainerWriter  writeSampleData 6com.llfbandit.record.record.container.IContainerWriter  writeStream 6com.llfbandit.record.record.container.IContainerWriter  
BufferInfo 0com.llfbandit.record.record.container.MediaCodec  
AtomicBoolean 4com.llfbandit.record.record.container.MuxerContainer  
MediaMuxer 4com.llfbandit.record.record.container.MuxerContainer  containerFormat 4com.llfbandit.record.record.container.MuxerContainer  mMuxer 4com.llfbandit.record.record.container.MuxerContainer  mStarted 4com.llfbandit.record.record.container.MuxerContainer  mStopped 4com.llfbandit.record.record.container.MuxerContainer  path 4com.llfbandit.record.record.container.MuxerContainer  stop 4com.llfbandit.record.record.container.MuxerContainer  	ByteArray 2com.llfbandit.record.record.container.RawContainer  IllegalStateException 2com.llfbandit.record.record.container.RawContainer  Os 2com.llfbandit.record.record.container.RawContainer  
createFile 2com.llfbandit.record.record.container.RawContainer  file 2com.llfbandit.record.record.container.RawContainer  	isStarted 2com.llfbandit.record.record.container.RawContainer  path 2com.llfbandit.record.record.container.RawContainer  stop 2com.llfbandit.record.record.container.RawContainer  track 2com.llfbandit.record.record.container.RawContainer  
ByteBuffer 3com.llfbandit.record.record.container.WaveContainer  	ByteOrder 3com.llfbandit.record.record.container.WaveContainer  
DATA_MAGIC 3com.llfbandit.record.record.container.WaveContainer  ExperimentalUnsignedTypes 3com.llfbandit.record.record.container.WaveContainer  	FMT_MAGIC 3com.llfbandit.record.record.container.WaveContainer  HEADER_SIZE 3com.llfbandit.record.record.container.WaveContainer  IllegalStateException 3com.llfbandit.record.record.container.WaveContainer  Int 3com.llfbandit.record.record.container.WaveContainer  Long 3com.llfbandit.record.record.container.WaveContainer  
MediaCodec 3com.llfbandit.record.record.container.WaveContainer  MediaFormat 3com.llfbandit.record.record.container.WaveContainer  Os 3com.llfbandit.record.record.container.WaveContainer  OsConstants 3com.llfbandit.record.record.container.WaveContainer  Pair 3com.llfbandit.record.record.container.WaveContainer  
RIFF_MAGIC 3com.llfbandit.record.record.container.WaveContainer  String 3com.llfbandit.record.record.container.WaveContainer  
WAVE_MAGIC 3com.llfbandit.record.record.container.WaveContainer  apply 3com.llfbandit.record.record.container.WaveContainer  asByteArray 3com.llfbandit.record.record.container.WaveContainer  buildHeader 3com.llfbandit.record.record.container.WaveContainer  channelCount 3com.llfbandit.record.record.container.WaveContainer  
createFile 3com.llfbandit.record.record.container.WaveContainer  file 3com.llfbandit.record.record.container.WaveContainer  	frameSize 3com.llfbandit.record.record.container.WaveContainer  	isStarted 3com.llfbandit.record.record.container.WaveContainer  
sampleRate 3com.llfbandit.record.record.container.WaveContainer  stop 3com.llfbandit.record.record.container.WaveContainer  track 3com.llfbandit.record.record.container.WaveContainer  ubyteArrayOf 3com.llfbandit.record.record.container.WaveContainer  
ByteBuffer =com.llfbandit.record.record.container.WaveContainer.Companion  	ByteOrder =com.llfbandit.record.record.container.WaveContainer.Companion  
DATA_MAGIC =com.llfbandit.record.record.container.WaveContainer.Companion  	FMT_MAGIC =com.llfbandit.record.record.container.WaveContainer.Companion  HEADER_SIZE =com.llfbandit.record.record.container.WaveContainer.Companion  IllegalStateException =com.llfbandit.record.record.container.WaveContainer.Companion  Int =com.llfbandit.record.record.container.WaveContainer.Companion  MediaFormat =com.llfbandit.record.record.container.WaveContainer.Companion  Os =com.llfbandit.record.record.container.WaveContainer.Companion  OsConstants =com.llfbandit.record.record.container.WaveContainer.Companion  Pair =com.llfbandit.record.record.container.WaveContainer.Companion  
RIFF_MAGIC =com.llfbandit.record.record.container.WaveContainer.Companion  
WAVE_MAGIC =com.llfbandit.record.record.container.WaveContainer.Companion  apply =com.llfbandit.record.record.container.WaveContainer.Companion  asByteArray =com.llfbandit.record.record.container.WaveContainer.Companion  channelCount =com.llfbandit.record.record.container.WaveContainer.Companion  	frameSize =com.llfbandit.record.record.container.WaveContainer.Companion  
sampleRate =com.llfbandit.record.record.container.WaveContainer.Companion  ubyteArrayOf =com.llfbandit.record.record.container.WaveContainer.Companion  
BufferInfo >com.llfbandit.record.record.container.WaveContainer.MediaCodec  AudioDeviceInfo "com.llfbandit.record.record.device  AudioManager "com.llfbandit.record.record.device  Build "com.llfbandit.record.record.device  Context "com.llfbandit.record.record.device  DeviceUtils "com.llfbandit.record.record.device  Int "com.llfbandit.record.record.device  List "com.llfbandit.record.record.device  Map "com.llfbandit.record.record.device  String "com.llfbandit.record.record.device  
StringBuilder "com.llfbandit.record.record.device  apply "com.llfbandit.record.record.device  asList "com.llfbandit.record.record.device  filter "com.llfbandit.record.record.device  firstOrNull "com.llfbandit.record.record.device  map "com.llfbandit.record.record.device  mapOf "com.llfbandit.record.record.device  to "com.llfbandit.record.record.device  typeToString "com.llfbandit.record.record.device  AudioDeviceInfo .com.llfbandit.record.record.device.DeviceUtils  AudioManager .com.llfbandit.record.record.device.DeviceUtils  Build .com.llfbandit.record.record.device.DeviceUtils  	Companion .com.llfbandit.record.record.device.DeviceUtils  Context .com.llfbandit.record.record.device.DeviceUtils  Int .com.llfbandit.record.record.device.DeviceUtils  List .com.llfbandit.record.record.device.DeviceUtils  Map .com.llfbandit.record.record.device.DeviceUtils  String .com.llfbandit.record.record.device.DeviceUtils  
StringBuilder .com.llfbandit.record.record.device.DeviceUtils  apply .com.llfbandit.record.record.device.DeviceUtils  asList .com.llfbandit.record.record.device.DeviceUtils  deviceInfoFromMap .com.llfbandit.record.record.device.DeviceUtils  filter .com.llfbandit.record.record.device.DeviceUtils  
filterSources .com.llfbandit.record.record.device.DeviceUtils  firstOrNull .com.llfbandit.record.record.device.DeviceUtils  listInputDevices .com.llfbandit.record.record.device.DeviceUtils  listInputDevicesAsMap .com.llfbandit.record.record.device.DeviceUtils  map .com.llfbandit.record.record.device.DeviceUtils  mapOf .com.llfbandit.record.record.device.DeviceUtils  to .com.llfbandit.record.record.device.DeviceUtils  typeToString .com.llfbandit.record.record.device.DeviceUtils  AudioDeviceInfo 8com.llfbandit.record.record.device.DeviceUtils.Companion  AudioManager 8com.llfbandit.record.record.device.DeviceUtils.Companion  Build 8com.llfbandit.record.record.device.DeviceUtils.Companion  Context 8com.llfbandit.record.record.device.DeviceUtils.Companion  
StringBuilder 8com.llfbandit.record.record.device.DeviceUtils.Companion  apply 8com.llfbandit.record.record.device.DeviceUtils.Companion  asList 8com.llfbandit.record.record.device.DeviceUtils.Companion  deviceInfoFromMap 8com.llfbandit.record.record.device.DeviceUtils.Companion  filter 8com.llfbandit.record.record.device.DeviceUtils.Companion  
filterSources 8com.llfbandit.record.record.device.DeviceUtils.Companion  firstOrNull 8com.llfbandit.record.record.device.DeviceUtils.Companion  listInputDevices 8com.llfbandit.record.record.device.DeviceUtils.Companion  listInputDevicesAsMap 8com.llfbandit.record.record.device.DeviceUtils.Companion  map 8com.llfbandit.record.record.device.DeviceUtils.Companion  mapOf 8com.llfbandit.record.record.device.DeviceUtils.Companion  to 8com.llfbandit.record.record.device.DeviceUtils.Companion  typeToString 8com.llfbandit.record.record.device.DeviceUtils.Companion  
AtomicBoolean #com.llfbandit.record.record.encoder  Boolean #com.llfbandit.record.record.encoder  	ByteArray #com.llfbandit.record.record.encoder  
ByteBuffer #com.llfbandit.record.record.encoder  EncoderListener #com.llfbandit.record.record.encoder  	Exception #com.llfbandit.record.record.encoder  Format #com.llfbandit.record.record.encoder  Handler #com.llfbandit.record.record.encoder  
HandlerThread #com.llfbandit.record.record.encoder  IContainerWriter #com.llfbandit.record.record.encoder  IEncoder #com.llfbandit.record.record.encoder  Int #com.llfbandit.record.record.encoder  
LinkedList #com.llfbandit.record.record.encoder  Log #com.llfbandit.record.record.encoder  Long #com.llfbandit.record.record.encoder  MSG_ENCODE_INPUT #com.llfbandit.record.record.encoder  MSG_INIT #com.llfbandit.record.record.encoder  MSG_STOP #com.llfbandit.record.record.encoder  
MediaCodec #com.llfbandit.record.record.encoder  MediaCodecEncoder #com.llfbandit.record.record.encoder  MediaFormat #com.llfbandit.record.record.encoder  Message #com.llfbandit.record.record.encoder  PassthroughEncoder #com.llfbandit.record.record.encoder  RecordConfig #com.llfbandit.record.record.encoder  Sample #com.llfbandit.record.record.encoder  	Semaphore #com.llfbandit.record.record.encoder  String #com.llfbandit.record.record.encoder  	divAssign #com.llfbandit.record.record.encoder  
mContainer #com.llfbandit.record.record.encoder  mContainerTrack #com.llfbandit.record.record.encoder  mInputBufferIndex #com.llfbandit.record.record.encoder  min #com.llfbandit.record.record.encoder  onError #com.llfbandit.record.record.encoder  
plusAssign #com.llfbandit.record.record.encoder  processInputBuffer #com.llfbandit.record.record.encoder  processOutputBuffer #com.llfbandit.record.record.encoder  timesAssign #com.llfbandit.record.record.encoder  onEncoderFailure 3com.llfbandit.record.record.encoder.EncoderListener  onEncoderStream 3com.llfbandit.record.record.encoder.EncoderListener  Callback +com.llfbandit.record.record.encoder.Handler  encode ,com.llfbandit.record.record.encoder.IEncoder  
startEncoding ,com.llfbandit.record.record.encoder.IEncoder  stopEncoding ,com.llfbandit.record.record.encoder.IEncoder  
BufferInfo .com.llfbandit.record.record.encoder.MediaCodec  Callback .com.llfbandit.record.record.encoder.MediaCodec  CodecException .com.llfbandit.record.record.encoder.MediaCodec  
AtomicBoolean 5com.llfbandit.record.record.encoder.MediaCodecEncoder  AudioRecorderCodecCallback 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Boolean 5com.llfbandit.record.record.encoder.MediaCodecEncoder  	ByteArray 5com.llfbandit.record.record.encoder.MediaCodecEncoder  EncoderListener 5com.llfbandit.record.record.encoder.MediaCodecEncoder  	Exception 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Format 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Handler 5com.llfbandit.record.record.encoder.MediaCodecEncoder  IContainerWriter 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Int 5com.llfbandit.record.record.encoder.MediaCodecEncoder  
LinkedList 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Log 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Long 5com.llfbandit.record.record.encoder.MediaCodecEncoder  MSG_ENCODE_INPUT 5com.llfbandit.record.record.encoder.MediaCodecEncoder  MSG_INIT 5com.llfbandit.record.record.encoder.MediaCodecEncoder  MSG_STOP 5com.llfbandit.record.record.encoder.MediaCodecEncoder  
MediaCodec 5com.llfbandit.record.record.encoder.MediaCodecEncoder  MediaFormat 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Message 5com.llfbandit.record.record.encoder.MediaCodecEncoder  RecordConfig 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Sample 5com.llfbandit.record.record.encoder.MediaCodecEncoder  	Semaphore 5com.llfbandit.record.record.encoder.MediaCodecEncoder  String 5com.llfbandit.record.record.encoder.MediaCodecEncoder  calculateInputRate 5com.llfbandit.record.record.encoder.MediaCodecEncoder  codec 5com.llfbandit.record.record.encoder.MediaCodecEncoder  config 5com.llfbandit.record.record.encoder.MediaCodecEncoder  	divAssign 5com.llfbandit.record.record.encoder.MediaCodecEncoder  format 5com.llfbandit.record.record.encoder.MediaCodecEncoder  getPresentationTimestampUs 5com.llfbandit.record.record.encoder.MediaCodecEncoder  initEncoding 5com.llfbandit.record.record.encoder.MediaCodecEncoder  listener 5com.llfbandit.record.record.encoder.MediaCodecEncoder  looper 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mCodec 5com.llfbandit.record.record.encoder.MediaCodecEncoder  
mContainer 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mContainerTrack 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mHandler 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mInputBufferIndex 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mInputBufferPosition 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mQueue 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mRate 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mStopped 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mStoppedCompleter 5com.llfbandit.record.record.encoder.MediaCodecEncoder  mediaFormat 5com.llfbandit.record.record.encoder.MediaCodecEncoder  min 5com.llfbandit.record.record.encoder.MediaCodecEncoder  onError 5com.llfbandit.record.record.encoder.MediaCodecEncoder  
plusAssign 5com.llfbandit.record.record.encoder.MediaCodecEncoder  processInputBuffer 5com.llfbandit.record.record.encoder.MediaCodecEncoder  processOutputBuffer 5com.llfbandit.record.record.encoder.MediaCodecEncoder  
quitSafely 5com.llfbandit.record.record.encoder.MediaCodecEncoder  start 5com.llfbandit.record.record.encoder.MediaCodecEncoder  stopAndRelease 5com.llfbandit.record.record.encoder.MediaCodecEncoder  timesAssign 5com.llfbandit.record.record.encoder.MediaCodecEncoder  Log Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  
mContainer Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  mContainerTrack Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  mInputBufferIndex Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  onError Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  processInputBuffer Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  processOutputBuffer Pcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback  
AtomicBoolean ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  Handler ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  
LinkedList ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  Log ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  MSG_ENCODE_INPUT ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  MSG_INIT ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  MSG_STOP ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  
MediaCodec ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  MediaFormat ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  	Semaphore ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  	divAssign ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  
mContainer ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  mContainerTrack ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  mInputBufferIndex ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  min ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  onError ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  
plusAssign ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  processInputBuffer ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  processOutputBuffer ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  timesAssign ?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion  
BufferInfo @com.llfbandit.record.record.encoder.MediaCodecEncoder.MediaCodec  Callback @com.llfbandit.record.record.encoder.MediaCodecEncoder.MediaCodec  CodecException @com.llfbandit.record.record.encoder.MediaCodecEncoder.MediaCodec  bytes <com.llfbandit.record.record.encoder.MediaCodecEncoder.Sample  offset <com.llfbandit.record.record.encoder.MediaCodecEncoder.Sample  
ByteBuffer 6com.llfbandit.record.record.encoder.PassthroughEncoder  Format 6com.llfbandit.record.record.encoder.PassthroughEncoder  
MediaCodec 6com.llfbandit.record.record.encoder.PassthroughEncoder  MediaFormat 6com.llfbandit.record.record.encoder.PassthroughEncoder  listener 6com.llfbandit.record.record.encoder.PassthroughEncoder  mBufferInfo 6com.llfbandit.record.record.encoder.PassthroughEncoder  
mContainer 6com.llfbandit.record.record.encoder.PassthroughEncoder  
mFrameSize 6com.llfbandit.record.record.encoder.PassthroughEncoder  
mIsStarted 6com.llfbandit.record.record.encoder.PassthroughEncoder  
mNumFrames 6com.llfbandit.record.record.encoder.PassthroughEncoder  mSampleRate 6com.llfbandit.record.record.encoder.PassthroughEncoder  mTrackIndex 6com.llfbandit.record.record.encoder.PassthroughEncoder  mediaFormat 6com.llfbandit.record.record.encoder.PassthroughEncoder  
plusAssign 6com.llfbandit.record.record.encoder.PassthroughEncoder  timestampUs 6com.llfbandit.record.record.encoder.PassthroughEncoder  	AacFormat "com.llfbandit.record.record.format  
AdtsContainer "com.llfbandit.record.record.format  AmrNbFormat "com.llfbandit.record.record.format  AmrWbFormat "com.llfbandit.record.record.format  AudioEncoder "com.llfbandit.record.record.format  AudioFormats "com.llfbandit.record.record.format  Boolean "com.llfbandit.record.record.format  Build "com.llfbandit.record.record.format  EncoderListener "com.llfbandit.record.record.format  	Exception "com.llfbandit.record.record.format  
FlacContainer "com.llfbandit.record.record.format  
FlacFormat "com.llfbandit.record.record.format  Format "com.llfbandit.record.record.format  IContainerWriter "com.llfbandit.record.record.format  IEncoder "com.llfbandit.record.record.format  IllegalAccessException "com.llfbandit.record.record.format  IllegalArgumentException "com.llfbandit.record.record.format  Int "com.llfbandit.record.record.format  IntArray "com.llfbandit.record.record.format  KEY_X_FRAME_SIZE_IN_BYTES "com.llfbandit.record.record.format  Log "com.llfbandit.record.record.format  MediaCodecEncoder "com.llfbandit.record.record.format  MediaCodecInfo "com.llfbandit.record.record.format  MediaCodecList "com.llfbandit.record.record.format  MediaFormat "com.llfbandit.record.record.format  
MediaMuxer "com.llfbandit.record.record.format  MuxerContainer "com.llfbandit.record.record.format  
OpusFormat "com.llfbandit.record.record.format  Pair "com.llfbandit.record.record.format  PassthroughEncoder "com.llfbandit.record.record.format  	PcmFormat "com.llfbandit.record.record.format  Range "com.llfbandit.record.record.format  RawContainer "com.llfbandit.record.record.format  RecordConfig "com.llfbandit.record.record.format  String "com.llfbandit.record.record.format  TAG "com.llfbandit.record.record.format  
WaveContainer "com.llfbandit.record.record.format  
WaveFormat "com.llfbandit.record.record.format  abs "com.llfbandit.record.record.format  apply "com.llfbandit.record.record.format  bitRates "com.llfbandit.record.record.format  equals "com.llfbandit.record.record.format  	frameSize "com.llfbandit.record.record.format  indices "com.llfbandit.record.record.format  
intArrayOf "com.llfbandit.record.record.format  java "com.llfbandit.record.record.format  map "com.llfbandit.record.record.format  
mimeTypeAudio "com.llfbandit.record.record.format  nearestValue "com.llfbandit.record.record.format  sampleRates "com.llfbandit.record.record.format  until "com.llfbandit.record.record.format  
AdtsContainer ,com.llfbandit.record.record.format.AacFormat  AudioEncoder ,com.llfbandit.record.record.format.AacFormat  IllegalArgumentException ,com.llfbandit.record.record.format.AacFormat  MediaCodecInfo ,com.llfbandit.record.record.format.AacFormat  MediaFormat ,com.llfbandit.record.record.format.AacFormat  
MediaMuxer ,com.llfbandit.record.record.format.AacFormat  MuxerContainer ,com.llfbandit.record.record.format.AacFormat  
aacProfile ,com.llfbandit.record.record.format.AacFormat  apply ,com.llfbandit.record.record.format.AacFormat  
mimeTypeAudio ,com.llfbandit.record.record.format.AacFormat  numChannels ,com.llfbandit.record.record.format.AacFormat  
sampleRate ,com.llfbandit.record.record.format.AacFormat  Build .com.llfbandit.record.record.format.AmrNbFormat  IllegalAccessException .com.llfbandit.record.record.format.AmrNbFormat  IllegalArgumentException .com.llfbandit.record.record.format.AmrNbFormat  MediaFormat .com.llfbandit.record.record.format.AmrNbFormat  
MediaMuxer .com.llfbandit.record.record.format.AmrNbFormat  MuxerContainer .com.llfbandit.record.record.format.AmrNbFormat  apply .com.llfbandit.record.record.format.AmrNbFormat  bitRates .com.llfbandit.record.record.format.AmrNbFormat  
intArrayOf .com.llfbandit.record.record.format.AmrNbFormat  
mimeTypeAudio .com.llfbandit.record.record.format.AmrNbFormat  nearestValue .com.llfbandit.record.record.format.AmrNbFormat  Build .com.llfbandit.record.record.format.AmrWbFormat  IllegalAccessException .com.llfbandit.record.record.format.AmrWbFormat  IllegalArgumentException .com.llfbandit.record.record.format.AmrWbFormat  MediaFormat .com.llfbandit.record.record.format.AmrWbFormat  
MediaMuxer .com.llfbandit.record.record.format.AmrWbFormat  MuxerContainer .com.llfbandit.record.record.format.AmrWbFormat  apply .com.llfbandit.record.record.format.AmrWbFormat  bitRates .com.llfbandit.record.record.format.AmrWbFormat  
intArrayOf .com.llfbandit.record.record.format.AmrWbFormat  
mimeTypeAudio .com.llfbandit.record.record.format.AmrWbFormat  nearestValue .com.llfbandit.record.record.format.AmrWbFormat  AudioEncoder /com.llfbandit.record.record.format.AudioFormats  MediaCodecList /com.llfbandit.record.record.format.AudioFormats  MediaFormat /com.llfbandit.record.record.format.AudioFormats  equals /com.llfbandit.record.record.format.AudioFormats  getMimeType /com.llfbandit.record.record.format.AudioFormats  isEncoderSupported /com.llfbandit.record.record.format.AudioFormats  
FlacContainer -com.llfbandit.record.record.format.FlacFormat  IllegalArgumentException -com.llfbandit.record.record.format.FlacFormat  MediaFormat -com.llfbandit.record.record.format.FlacFormat  apply -com.llfbandit.record.record.format.FlacFormat  
intArrayOf -com.llfbandit.record.record.format.FlacFormat  
mimeTypeAudio -com.llfbandit.record.record.format.FlacFormat  nearestValue -com.llfbandit.record.record.format.FlacFormat  sampleRates -com.llfbandit.record.record.format.FlacFormat  
AdtsContainer )com.llfbandit.record.record.format.Format  AudioEncoder )com.llfbandit.record.record.format.Format  Boolean )com.llfbandit.record.record.format.Format  Build )com.llfbandit.record.record.format.Format  	Companion )com.llfbandit.record.record.format.Format  EncoderListener )com.llfbandit.record.record.format.Format  	Exception )com.llfbandit.record.record.format.Format  
FlacContainer )com.llfbandit.record.record.format.Format  Format )com.llfbandit.record.record.format.Format  IContainerWriter )com.llfbandit.record.record.format.Format  IEncoder )com.llfbandit.record.record.format.Format  IllegalAccessException )com.llfbandit.record.record.format.Format  IllegalArgumentException )com.llfbandit.record.record.format.Format  Int )com.llfbandit.record.record.format.Format  IntArray )com.llfbandit.record.record.format.Format  KEY_X_FRAME_SIZE_IN_BYTES )com.llfbandit.record.record.format.Format  Log )com.llfbandit.record.record.format.Format  MediaCodecEncoder )com.llfbandit.record.record.format.Format  MediaCodecInfo )com.llfbandit.record.record.format.Format  MediaCodecList )com.llfbandit.record.record.format.Format  MediaFormat )com.llfbandit.record.record.format.Format  
MediaMuxer )com.llfbandit.record.record.format.Format  MuxerContainer )com.llfbandit.record.record.format.Format  Pair )com.llfbandit.record.record.format.Format  PassthroughEncoder )com.llfbandit.record.record.format.Format  Range )com.llfbandit.record.record.format.Format  RawContainer )com.llfbandit.record.record.format.Format  RecordConfig )com.llfbandit.record.record.format.Format  String )com.llfbandit.record.record.format.Format  TAG )com.llfbandit.record.record.format.Format  
WaveContainer )com.llfbandit.record.record.format.Format  abs )com.llfbandit.record.record.format.Format  
adjustBitRate )com.llfbandit.record.record.format.Format  adjustFormat )com.llfbandit.record.record.format.Format  adjustNumChannels )com.llfbandit.record.record.format.Format  adjustSampleRate )com.llfbandit.record.record.format.Format  apply )com.llfbandit.record.record.format.Format  bitRates )com.llfbandit.record.record.format.Format  checkBounds )com.llfbandit.record.record.format.Format  findCodecForAdjustedFormat )com.llfbandit.record.record.format.Format  	frameSize )com.llfbandit.record.record.format.Format  getContainer )com.llfbandit.record.record.format.Format  
getEncoder )com.llfbandit.record.record.format.Format  getMediaFormat )com.llfbandit.record.record.format.Format  indices )com.llfbandit.record.record.format.Format  
intArrayOf )com.llfbandit.record.record.format.Format  java )com.llfbandit.record.record.format.Format  map )com.llfbandit.record.record.format.Format  
mimeTypeAudio )com.llfbandit.record.record.format.Format  nearestValue )com.llfbandit.record.record.format.Format  passthrough )com.llfbandit.record.record.format.Format  sampleRates )com.llfbandit.record.record.format.Format  until )com.llfbandit.record.record.format.Format  
AdtsContainer 3com.llfbandit.record.record.format.Format.Companion  AudioEncoder 3com.llfbandit.record.record.format.Format.Companion  Build 3com.llfbandit.record.record.format.Format.Companion  	Exception 3com.llfbandit.record.record.format.Format.Companion  
FlacContainer 3com.llfbandit.record.record.format.Format.Companion  Format 3com.llfbandit.record.record.format.Format.Companion  IllegalAccessException 3com.llfbandit.record.record.format.Format.Companion  IllegalArgumentException 3com.llfbandit.record.record.format.Format.Companion  KEY_X_FRAME_SIZE_IN_BYTES 3com.llfbandit.record.record.format.Format.Companion  Log 3com.llfbandit.record.record.format.Format.Companion  MediaCodecEncoder 3com.llfbandit.record.record.format.Format.Companion  MediaCodecInfo 3com.llfbandit.record.record.format.Format.Companion  MediaCodecList 3com.llfbandit.record.record.format.Format.Companion  MediaFormat 3com.llfbandit.record.record.format.Format.Companion  
MediaMuxer 3com.llfbandit.record.record.format.Format.Companion  MuxerContainer 3com.llfbandit.record.record.format.Format.Companion  Pair 3com.llfbandit.record.record.format.Format.Companion  PassthroughEncoder 3com.llfbandit.record.record.format.Format.Companion  RawContainer 3com.llfbandit.record.record.format.Format.Companion  TAG 3com.llfbandit.record.record.format.Format.Companion  
WaveContainer 3com.llfbandit.record.record.format.Format.Companion  abs 3com.llfbandit.record.record.format.Format.Companion  apply 3com.llfbandit.record.record.format.Format.Companion  bitRates 3com.llfbandit.record.record.format.Format.Companion  	frameSize 3com.llfbandit.record.record.format.Format.Companion  indices 3com.llfbandit.record.record.format.Format.Companion  
intArrayOf 3com.llfbandit.record.record.format.Format.Companion  java 3com.llfbandit.record.record.format.Format.Companion  map 3com.llfbandit.record.record.format.Format.Companion  
mimeTypeAudio 3com.llfbandit.record.record.format.Format.Companion  nearestValue 3com.llfbandit.record.record.format.Format.Companion  sampleRates 3com.llfbandit.record.record.format.Format.Companion  until 3com.llfbandit.record.record.format.Format.Companion  CodecCapabilities 8com.llfbandit.record.record.format.Format.MediaCodecInfo  CodecCapabilities 1com.llfbandit.record.record.format.MediaCodecInfo  Build -com.llfbandit.record.record.format.OpusFormat  IllegalAccessException -com.llfbandit.record.record.format.OpusFormat  IllegalArgumentException -com.llfbandit.record.record.format.OpusFormat  MediaFormat -com.llfbandit.record.record.format.OpusFormat  
MediaMuxer -com.llfbandit.record.record.format.OpusFormat  MuxerContainer -com.llfbandit.record.record.format.OpusFormat  apply -com.llfbandit.record.record.format.OpusFormat  
intArrayOf -com.llfbandit.record.record.format.OpusFormat  
mimeTypeAudio -com.llfbandit.record.record.format.OpusFormat  nearestValue -com.llfbandit.record.record.format.OpusFormat  sampleRates -com.llfbandit.record.record.format.OpusFormat  KEY_X_FRAME_SIZE_IN_BYTES ,com.llfbandit.record.record.format.PcmFormat  MediaFormat ,com.llfbandit.record.record.format.PcmFormat  RawContainer ,com.llfbandit.record.record.format.PcmFormat  apply ,com.llfbandit.record.record.format.PcmFormat  
mimeTypeAudio ,com.llfbandit.record.record.format.PcmFormat  IllegalArgumentException -com.llfbandit.record.record.format.WaveFormat  KEY_X_FRAME_SIZE_IN_BYTES -com.llfbandit.record.record.format.WaveFormat  MediaFormat -com.llfbandit.record.record.format.WaveFormat  
WaveContainer -com.llfbandit.record.record.format.WaveFormat  apply -com.llfbandit.record.record.format.WaveFormat  	frameSize -com.llfbandit.record.record.format.WaveFormat  
mimeTypeAudio -com.llfbandit.record.record.format.WaveFormat  	AacFormat $com.llfbandit.record.record.recorder  AmrNbFormat $com.llfbandit.record.record.recorder  AmrWbFormat $com.llfbandit.record.record.recorder  	ArrayList $com.llfbandit.record.record.recorder  
AtomicBoolean $com.llfbandit.record.record.recorder  AudioEncoder $com.llfbandit.record.record.recorder  AudioManager $com.llfbandit.record.record.recorder  
AudioRecorder $com.llfbandit.record.record.recorder  Boolean $com.llfbandit.record.record.recorder  Build $com.llfbandit.record.record.recorder  	ByteArray $com.llfbandit.record.record.recorder  Context $com.llfbandit.record.record.recorder  CountDownLatch $com.llfbandit.record.record.recorder  Double $com.llfbandit.record.record.recorder  EncoderListener $com.llfbandit.record.record.recorder  	Exception $com.llfbandit.record.record.recorder  	Executors $com.llfbandit.record.record.recorder  
FlacFormat $com.llfbandit.record.record.recorder  Format $com.llfbandit.record.record.recorder  HashMap $com.llfbandit.record.record.recorder  IEncoder $com.llfbandit.record.record.recorder  IOException $com.llfbandit.record.record.recorder  	IRecorder $com.llfbandit.record.record.recorder  IllegalStateException $com.llfbandit.record.record.recorder  Int $com.llfbandit.record.record.recorder  List $com.llfbandit.record.record.recorder  Log $com.llfbandit.record.record.recorder  
MediaRecorder $com.llfbandit.record.record.recorder  MutableList $com.llfbandit.record.record.recorder  OnAudioRecordListener $com.llfbandit.record.record.recorder  
OpusFormat $com.llfbandit.record.record.recorder  	PCMReader $com.llfbandit.record.record.recorder  	PcmFormat $com.llfbandit.record.record.recorder  RecordConfig $com.llfbandit.record.record.recorder  RecordState $com.llfbandit.record.record.recorder  RecordThread $com.llfbandit.record.record.recorder  RecorderRecordStreamHandler $com.llfbandit.record.record.recorder  RecorderStateStreamHandler $com.llfbandit.record.record.recorder  RequiresApi $com.llfbandit.record.record.recorder  RuntimeException $com.llfbandit.record.record.recorder  	Semaphore $com.llfbandit.record.record.recorder  String $com.llfbandit.record.record.recorder  Suppress $com.llfbandit.record.record.recorder  TAG $com.llfbandit.record.record.recorder  Throws $com.llfbandit.record.record.recorder  Unit $com.llfbandit.record.record.recorder  Utils $com.llfbandit.record.record.recorder  
WaveFormat $com.llfbandit.record.record.recorder  arrayOf $com.llfbandit.record.record.recorder  
coerceAtLeast $com.llfbandit.record.record.recorder  coerceAtMost $com.llfbandit.record.record.recorder  forEach $com.llfbandit.record.record.recorder  
isNotEmpty $com.llfbandit.record.record.recorder  java $com.llfbandit.record.record.recorder  log10 $com.llfbandit.record.record.recorder  set $com.llfbandit.record.record.recorder  
trimIndent $com.llfbandit.record.record.recorder  	ArrayList 2com.llfbandit.record.record.recorder.AudioRecorder  AudioManager 2com.llfbandit.record.record.recorder.AudioRecorder  
AudioRecorder 2com.llfbandit.record.record.recorder.AudioRecorder  Boolean 2com.llfbandit.record.record.recorder.AudioRecorder  	ByteArray 2com.llfbandit.record.record.recorder.AudioRecorder  	Companion 2com.llfbandit.record.record.recorder.AudioRecorder  Context 2com.llfbandit.record.record.recorder.AudioRecorder  Double 2com.llfbandit.record.record.recorder.AudioRecorder  	Exception 2com.llfbandit.record.record.recorder.AudioRecorder  HashMap 2com.llfbandit.record.record.recorder.AudioRecorder  Int 2com.llfbandit.record.record.recorder.AudioRecorder  List 2com.llfbandit.record.record.recorder.AudioRecorder  Log 2com.llfbandit.record.record.recorder.AudioRecorder  MutableList 2com.llfbandit.record.record.recorder.AudioRecorder  RecordConfig 2com.llfbandit.record.record.recorder.AudioRecorder  RecordState 2com.llfbandit.record.record.recorder.AudioRecorder  RecordThread 2com.llfbandit.record.record.recorder.AudioRecorder  RecorderRecordStreamHandler 2com.llfbandit.record.record.recorder.AudioRecorder  RecorderStateStreamHandler 2com.llfbandit.record.record.recorder.AudioRecorder  String 2com.llfbandit.record.record.recorder.AudioRecorder  Suppress 2com.llfbandit.record.record.recorder.AudioRecorder  TAG 2com.llfbandit.record.record.recorder.AudioRecorder  Throws 2com.llfbandit.record.record.recorder.AudioRecorder  Unit 2com.llfbandit.record.record.recorder.AudioRecorder  amPrevAudioMode 2com.llfbandit.record.record.recorder.AudioRecorder  amPrevMuteSettings 2com.llfbandit.record.record.recorder.AudioRecorder  amPrevSpeakerphone 2com.llfbandit.record.record.recorder.AudioRecorder  
appContext 2com.llfbandit.record.record.recorder.AudioRecorder  arrayOf 2com.llfbandit.record.record.recorder.AudioRecorder  assignAudioManagerSettings 2com.llfbandit.record.record.recorder.AudioRecorder  config 2com.llfbandit.record.record.recorder.AudioRecorder  forEach 2com.llfbandit.record.record.recorder.AudioRecorder  java 2com.llfbandit.record.record.recorder.AudioRecorder  maxAmplitude 2com.llfbandit.record.record.recorder.AudioRecorder  	muteAudio 2com.llfbandit.record.record.recorder.AudioRecorder  muteStreams 2com.llfbandit.record.record.recorder.AudioRecorder  recorderRecordStreamHandler 2com.llfbandit.record.record.recorder.AudioRecorder  recorderStateStreamHandler 2com.llfbandit.record.record.recorder.AudioRecorder  recorderThread 2com.llfbandit.record.record.recorder.AudioRecorder  restoreAudioManagerSettings 2com.llfbandit.record.record.recorder.AudioRecorder  saveAudioManagerSettings 2com.llfbandit.record.record.recorder.AudioRecorder  set 2com.llfbandit.record.record.recorder.AudioRecorder  stop 2com.llfbandit.record.record.recorder.AudioRecorder  stopCb 2com.llfbandit.record.record.recorder.AudioRecorder  	ArrayList <com.llfbandit.record.record.recorder.AudioRecorder.Companion  AudioManager <com.llfbandit.record.record.recorder.AudioRecorder.Companion  
AudioRecorder <com.llfbandit.record.record.recorder.AudioRecorder.Companion  Context <com.llfbandit.record.record.recorder.AudioRecorder.Companion  	Exception <com.llfbandit.record.record.recorder.AudioRecorder.Companion  HashMap <com.llfbandit.record.record.recorder.AudioRecorder.Companion  Log <com.llfbandit.record.record.recorder.AudioRecorder.Companion  RecordState <com.llfbandit.record.record.recorder.AudioRecorder.Companion  RecordThread <com.llfbandit.record.record.recorder.AudioRecorder.Companion  TAG <com.llfbandit.record.record.recorder.AudioRecorder.Companion  arrayOf <com.llfbandit.record.record.recorder.AudioRecorder.Companion  forEach <com.llfbandit.record.record.recorder.AudioRecorder.Companion  java <com.llfbandit.record.record.recorder.AudioRecorder.Companion  set <com.llfbandit.record.record.recorder.AudioRecorder.Companion  	Exception .com.llfbandit.record.record.recorder.IRecorder  cancel .com.llfbandit.record.record.recorder.IRecorder  dispose .com.llfbandit.record.record.recorder.IRecorder  getAmplitude .com.llfbandit.record.record.recorder.IRecorder  isPaused .com.llfbandit.record.record.recorder.IRecorder  isRecording .com.llfbandit.record.record.recorder.IRecorder  pause .com.llfbandit.record.record.recorder.IRecorder  resume .com.llfbandit.record.record.recorder.IRecorder  start .com.llfbandit.record.record.recorder.IRecorder  stop .com.llfbandit.record.record.recorder.IRecorder  	ArrayList 2com.llfbandit.record.record.recorder.MediaRecorder  AudioEncoder 2com.llfbandit.record.record.recorder.MediaRecorder  Boolean 2com.llfbandit.record.record.recorder.MediaRecorder  Build 2com.llfbandit.record.record.recorder.MediaRecorder  Context 2com.llfbandit.record.record.recorder.MediaRecorder  Double 2com.llfbandit.record.record.recorder.MediaRecorder  	Exception 2com.llfbandit.record.record.recorder.MediaRecorder  IOException 2com.llfbandit.record.record.recorder.MediaRecorder  IllegalStateException 2com.llfbandit.record.record.recorder.MediaRecorder  Int 2com.llfbandit.record.record.recorder.MediaRecorder  List 2com.llfbandit.record.record.recorder.MediaRecorder  Log 2com.llfbandit.record.record.recorder.MediaRecorder  
MediaRecorder 2com.llfbandit.record.record.recorder.MediaRecorder  MutableList 2com.llfbandit.record.record.recorder.MediaRecorder  RecordConfig 2com.llfbandit.record.record.recorder.MediaRecorder  RecordState 2com.llfbandit.record.record.recorder.MediaRecorder  RecorderStateStreamHandler 2com.llfbandit.record.record.recorder.MediaRecorder  RequiresApi 2com.llfbandit.record.record.recorder.MediaRecorder  RuntimeException 2com.llfbandit.record.record.recorder.MediaRecorder  String 2com.llfbandit.record.record.recorder.MediaRecorder  Suppress 2com.llfbandit.record.record.recorder.MediaRecorder  TAG 2com.llfbandit.record.record.recorder.MediaRecorder  Throws 2com.llfbandit.record.record.recorder.MediaRecorder  Unit 2com.llfbandit.record.record.recorder.MediaRecorder  Utils 2com.llfbandit.record.record.recorder.MediaRecorder  
coerceAtLeast 2com.llfbandit.record.record.recorder.MediaRecorder  coerceAtMost 2com.llfbandit.record.record.recorder.MediaRecorder  context 2com.llfbandit.record.record.recorder.MediaRecorder  
getEncoder 2com.llfbandit.record.record.recorder.MediaRecorder  getOutputFormat 2com.llfbandit.record.record.recorder.MediaRecorder  java 2com.llfbandit.record.record.recorder.MediaRecorder  log10 2com.llfbandit.record.record.recorder.MediaRecorder  mConfig 2com.llfbandit.record.record.recorder.MediaRecorder  	mIsPaused 2com.llfbandit.record.record.recorder.MediaRecorder  mIsRecording 2com.llfbandit.record.record.recorder.MediaRecorder  
mMaxAmplitude 2com.llfbandit.record.record.recorder.MediaRecorder  	mRecorder 2com.llfbandit.record.record.recorder.MediaRecorder  pauseRecording 2com.llfbandit.record.record.recorder.MediaRecorder  recorderStateStreamHandler 2com.llfbandit.record.record.recorder.MediaRecorder  resumeRecording 2com.llfbandit.record.record.recorder.MediaRecorder  
stopRecording 2com.llfbandit.record.record.recorder.MediaRecorder  
trimIndent 2com.llfbandit.record.record.recorder.MediaRecorder  updateState 2com.llfbandit.record.record.recorder.MediaRecorder  	ArrayList <com.llfbandit.record.record.recorder.MediaRecorder.Companion  AudioEncoder <com.llfbandit.record.record.recorder.MediaRecorder.Companion  Build <com.llfbandit.record.record.recorder.MediaRecorder.Companion  	Exception <com.llfbandit.record.record.recorder.MediaRecorder.Companion  Log <com.llfbandit.record.record.recorder.MediaRecorder.Companion  
MediaRecorder <com.llfbandit.record.record.recorder.MediaRecorder.Companion  RecordState <com.llfbandit.record.record.recorder.MediaRecorder.Companion  TAG <com.llfbandit.record.record.recorder.MediaRecorder.Companion  Utils <com.llfbandit.record.record.recorder.MediaRecorder.Companion  
coerceAtLeast <com.llfbandit.record.record.recorder.MediaRecorder.Companion  coerceAtMost <com.llfbandit.record.record.recorder.MediaRecorder.Companion  java <com.llfbandit.record.record.recorder.MediaRecorder.Companion  log10 <com.llfbandit.record.record.recorder.MediaRecorder.Companion  
trimIndent <com.llfbandit.record.record.recorder.MediaRecorder.Companion  onAudioChunk :com.llfbandit.record.record.recorder.OnAudioRecordListener  	onFailure :com.llfbandit.record.record.recorder.OnAudioRecordListener  onPause :com.llfbandit.record.record.recorder.OnAudioRecordListener  onRecord :com.llfbandit.record.record.recorder.OnAudioRecordListener  onStop :com.llfbandit.record.record.recorder.OnAudioRecordListener  	AacFormat 1com.llfbandit.record.record.recorder.RecordThread  AmrNbFormat 1com.llfbandit.record.record.recorder.RecordThread  AmrWbFormat 1com.llfbandit.record.record.recorder.RecordThread  
AtomicBoolean 1com.llfbandit.record.record.recorder.RecordThread  AudioEncoder 1com.llfbandit.record.record.recorder.RecordThread  CountDownLatch 1com.llfbandit.record.record.recorder.RecordThread  	Exception 1com.llfbandit.record.record.recorder.RecordThread  	Executors 1com.llfbandit.record.record.recorder.RecordThread  
FlacFormat 1com.llfbandit.record.record.recorder.RecordThread  
OpusFormat 1com.llfbandit.record.record.recorder.RecordThread  	PCMReader 1com.llfbandit.record.record.recorder.RecordThread  	PcmFormat 1com.llfbandit.record.record.recorder.RecordThread  	Semaphore 1com.llfbandit.record.record.recorder.RecordThread  Utils 1com.llfbandit.record.record.recorder.RecordThread  
WaveFormat 1com.llfbandit.record.record.recorder.RecordThread  cancelRecording 1com.llfbandit.record.record.recorder.RecordThread  config 1com.llfbandit.record.record.recorder.RecordThread  getAmplitude 1com.llfbandit.record.record.recorder.RecordThread  
isNotEmpty 1com.llfbandit.record.record.recorder.RecordThread  isPaused 1com.llfbandit.record.record.recorder.RecordThread  isRecording 1com.llfbandit.record.record.recorder.RecordThread  mEncoder 1com.llfbandit.record.record.recorder.RecordThread  mExecutorService 1com.llfbandit.record.record.recorder.RecordThread  mHasBeenCanceled 1com.llfbandit.record.record.recorder.RecordThread  	mIsPaused 1com.llfbandit.record.record.recorder.RecordThread  mIsPausedSem 1com.llfbandit.record.record.recorder.RecordThread  mIsRecording 1com.llfbandit.record.record.recorder.RecordThread  
mPcmReader 1com.llfbandit.record.record.recorder.RecordThread  pauseRecording 1com.llfbandit.record.record.recorder.RecordThread  
pauseState 1com.llfbandit.record.record.recorder.RecordThread  recordState 1com.llfbandit.record.record.recorder.RecordThread  recorderListener 1com.llfbandit.record.record.recorder.RecordThread  resumeRecording 1com.llfbandit.record.record.recorder.RecordThread  selectFormat 1com.llfbandit.record.record.recorder.RecordThread  startRecording 1com.llfbandit.record.record.recorder.RecordThread  stopAndRelease 1com.llfbandit.record.record.recorder.RecordThread  
stopRecording 1com.llfbandit.record.record.recorder.RecordThread  Any "com.llfbandit.record.record.stream  	ByteArray "com.llfbandit.record.record.stream  EventChannel "com.llfbandit.record.record.stream  	EventSink "com.llfbandit.record.record.stream  	Exception "com.llfbandit.record.record.stream  Handler "com.llfbandit.record.record.stream  Looper "com.llfbandit.record.record.stream  RecordState "com.llfbandit.record.record.stream  RecorderRecordStreamHandler "com.llfbandit.record.record.stream  RecorderStateStreamHandler "com.llfbandit.record.record.stream  
StreamHandler /com.llfbandit.record.record.stream.EventChannel  Handler >com.llfbandit.record.record.stream.RecorderRecordStreamHandler  Looper >com.llfbandit.record.record.stream.RecorderRecordStreamHandler  	eventSink >com.llfbandit.record.record.stream.RecorderRecordStreamHandler  sendRecordChunkEvent >com.llfbandit.record.record.stream.RecorderRecordStreamHandler  uiThreadHandler >com.llfbandit.record.record.stream.RecorderRecordStreamHandler  Handler =com.llfbandit.record.record.stream.RecorderStateStreamHandler  Looper =com.llfbandit.record.record.stream.RecorderStateStreamHandler  RecordState =com.llfbandit.record.record.stream.RecorderStateStreamHandler  	eventSink =com.llfbandit.record.record.stream.RecorderStateStreamHandler  sendStateErrorEvent =com.llfbandit.record.record.stream.RecorderStateStreamHandler  sendStateEvent =com.llfbandit.record.record.stream.RecorderStateStreamHandler  state =com.llfbandit.record.record.stream.RecorderStateStreamHandler  uiThreadHandler =com.llfbandit.record.record.stream.RecorderStateStreamHandler  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  IOException java.io  RandomAccessFile java.io  message java.io.IOException  close java.io.RandomAccessFile  fd java.io.RandomAccessFile  	setLength java.io.RandomAccessFile  
Appendable 	java.lang  Class 	java.lang  	Exception 	java.lang  IllegalAccessException 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  Runnable 	java.lang  RuntimeException 	java.lang  
StringBuilder 	java.lang  
simpleName java.lang.Class  cause java.lang.Exception  message java.lang.Exception  message java.lang.IllegalStateException  <SAM-CONSTRUCTOR> java.lang.Runnable  Build java.lang.StringBuilder  append java.lang.StringBuilder  
appendLine java.lang.StringBuilder  apply java.lang.StringBuilder  toString java.lang.StringBuilder  typeToString java.lang.StringBuilder  
AtomicBoolean java.lang.Thread  	Exception java.lang.Thread  Handler java.lang.Thread  
LinkedList java.lang.Thread  Log java.lang.Thread  MSG_ENCODE_INPUT java.lang.Thread  MSG_INIT java.lang.Thread  MSG_STOP java.lang.Thread  
MediaCodec java.lang.Thread  MediaFormat java.lang.Thread  Sample java.lang.Thread  	Semaphore java.lang.Thread  	divAssign java.lang.Thread  
mContainer java.lang.Thread  mContainerTrack java.lang.Thread  mInputBufferIndex java.lang.Thread  min java.lang.Thread  onError java.lang.Thread  
plusAssign java.lang.Thread  processInputBuffer java.lang.Thread  processOutputBuffer java.lang.Thread  start java.lang.Thread  timesAssign java.lang.Thread  
BigDecimal 	java.math  
BigInteger 	java.math  
ByteBuffer java.nio  	ByteOrder java.nio  capacity java.nio.Buffer  limit java.nio.Buffer  position java.nio.Buffer  	remaining java.nio.Buffer  allocate java.nio.ByteBuffer  
asShortBuffer java.nio.ByteBuffer  capacity java.nio.ByteBuffer  flip java.nio.ByteBuffer  get java.nio.ByteBuffer  limit java.nio.ByteBuffer  order java.nio.ByteBuffer  position java.nio.ByteBuffer  put java.nio.ByteBuffer  putInt java.nio.ByteBuffer  putShort java.nio.ByteBuffer  wrap java.nio.ByteBuffer  
LITTLE_ENDIAN java.nio.ByteOrder  get java.nio.ShortBuffer  	ArrayList 	java.util  HashMap 	java.util  HashSet 	java.util  
LinkedList 	java.util  Objects 	java.util  clear java.util.HashMap  get java.util.HashMap  set java.util.HashMap  add java.util.HashSet  addAll java.util.HashSet  any java.util.HashSet  clear java.util.HashSet  
isNotEmpty java.util.HashSet  remove java.util.HashSet  	removeAll java.util.HashSet  add java.util.LinkedList  	peekFirst java.util.LinkedList  pop java.util.LinkedList  requireNonNull java.util.Objects  ConcurrentHashMap java.util.concurrent  CountDownLatch java.util.concurrent  	Executors java.util.concurrent  	Semaphore java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  entries &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  await #java.util.concurrent.CountDownLatch  	countDown #java.util.concurrent.CountDownLatch  execute java.util.concurrent.Executor  newSingleThreadExecutor java.util.concurrent.Executors  acquire java.util.concurrent.Semaphore  release java.util.concurrent.Semaphore  
AtomicBoolean java.util.concurrent.atomic  get )java.util.concurrent.atomic.AtomicBoolean  set )java.util.concurrent.atomic.AtomicBoolean  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  	Exception kotlin  ExperimentalUnsignedTypes kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  IntArray kotlin  	LongArray kotlin  NotImplementedError kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  Suppress kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  apply kotlin  arrayOf kotlin  check kotlin  
intArrayOf kotlin  map kotlin  plus kotlin  to kotlin  toUByte kotlin  toULong kotlin  ubyteArrayOf kotlin  equals 
kotlin.Any  asList kotlin.Array  forEach kotlin.Array  iterator kotlin.Array  not kotlin.Boolean  
isNotEmpty kotlin.ByteArray  plus kotlin.ByteArray  
plusAssign kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  	compareTo 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  
unaryMinus 
kotlin.Double  Int kotlin.Enum  div kotlin.Float  	divAssign kotlin.Float  times kotlin.Float  timesAssign kotlin.Float  toLong kotlin.Float  invoke kotlin.Function1  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  and 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  shl 
kotlin.Int  shr 
kotlin.Int  times 
kotlin.Int  toByte 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toShort 
kotlin.Int  toString 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  get kotlin.IntArray  indexOf kotlin.IntArray  indices kotlin.IntArray  
isNotEmpty kotlin.IntArray  size kotlin.IntArray  	compareTo kotlin.Long  div kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  times kotlin.Long  toInt kotlin.Long  toULong kotlin.Long  
unaryMinus kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  toInt kotlin.Short  iterator kotlin.ShortArray  equals 
kotlin.String  
isNullOrEmpty 
kotlin.String  plus 
kotlin.String  to 
kotlin.String  
trimIndent 
kotlin.String  cause kotlin.Throwable  message kotlin.Throwable  and kotlin.UByte  or kotlin.UByte  toUInt kotlin.UByte  asByteArray kotlin.UByteArray  get kotlin.UByteArray  set kotlin.UByteArray  size kotlin.UByteArray  	compareTo kotlin.UInt  or kotlin.UInt  shl kotlin.UInt  shr kotlin.UInt  and kotlin.ULong  	compareTo kotlin.ULong  div kotlin.ULong  shl kotlin.ULong  shr kotlin.ULong  times kotlin.ULong  toUByte kotlin.ULong  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  
ShortIterator kotlin.collections  any kotlin.collections  asByteArray kotlin.collections  asList kotlin.collections  filter kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  get kotlin.collections  indexOf kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  map kotlin.collections  mapOf kotlin.collections  min kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  toSet kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  filter kotlin.collections.List  firstOrNull kotlin.collections.List  get kotlin.collections.List  map kotlin.collections.List  toSet kotlin.collections.List  get kotlin.collections.Map  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  set kotlin.collections.MutableMap  key *kotlin.collections.MutableMap.MutableEntry  value *kotlin.collections.MutableMap.MutableEntry  iterator kotlin.collections.MutableSet  hasNext  kotlin.collections.ShortIterator  next  kotlin.collections.ShortIterator  Throws 
kotlin.jvm  java 
kotlin.jvm  abs kotlin.math  log10 kotlin.math  min kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  firstOrNull 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  map kotlin.ranges.IntRange  
KFunction1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  filter kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  map kotlin.sequences  min kotlin.sequences  plus kotlin.sequences  toSet kotlin.sequences  
MatchGroup kotlin.text  any kotlin.text  
appendLine kotlin.text  equals kotlin.text  filter kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  get kotlin.text  indexOf kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  map kotlin.text  min kotlin.text  plus kotlin.text  set kotlin.text  toSet kotlin.text  toUByte kotlin.text  toULong kotlin.text  
trimIndent kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            