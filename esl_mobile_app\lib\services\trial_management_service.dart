import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

class TrialManagementService {
  static const String _baseUrl = 'https://talktoai.in';
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _trialShownKey = 'trial_welcome_shown';
  static const String _lastNotificationKey = 'last_trial_notification';
  
  static final FlutterLocalNotificationsPlugin _notificationsPlugin = 
      FlutterLocalNotificationsPlugin();

  // Initialize notification service
  static Future<void> initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
    );
    
    await _notificationsPlugin.initialize(initializationSettings);
  }

  // Check if trial welcome popup should be shown
  static Future<bool> shouldShowTrialWelcome() async {
    final shown = await _storage.read(key: _trialShownKey);
    return shown == null;
  }

  // Mark trial welcome as shown
  static Future<void> markTrialWelcomeShown() async {
    await _storage.write(key: _trialShownKey, value: 'true');
  }

  // Fetch user plan details
  static Future<Map<String, dynamic>?> fetchUserPlan() async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) {
      print('Trial System: No access token found');
      return null;
    }

    try {
      print('Trial System: Fetching user plan from API...');
      final response = await http.get(
        Uri.parse('$_baseUrl/get_user_plan'),
        headers: {'Authorization': 'Bearer $token'},
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final planData = jsonDecode(utf8.decode(response.bodyBytes));
        print('Trial System: Plan fetched successfully');
        print('Plan Status: ${planData['status']}');
        print('Days Remaining: ${planData['days_remaining']}');
        print('Plan ID: ${planData['plan_id']}');
        print('Plan expiry date: ${planData['expiry_date']}');
        return planData;
      } else if (response.statusCode == 401) {
        print('Trial System: Token expired, attempting refresh...');
        // Try to refresh token
        final refreshedPlan = await _refreshTokenAndRetry();
        return refreshedPlan;
      } else {
        print('Trial System: API error - Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Trial System: Error fetching user plan: $e');
      return null;
    }
  }

  // Refresh token and retry plan fetch
  static Future<Map<String, dynamic>?> _refreshTokenAndRetry() async {
    final refreshToken = await _storage.read(key: 'refresh_token');
    if (refreshToken == null) return null;

    try {
      final refreshResponse = await http.post(
        Uri.parse('https://talktoai.in:8001/refresh-token'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'refresh_token': refreshToken}),
      ).timeout(const Duration(seconds: 10));

      if (refreshResponse.statusCode == 200) {
        final newToken = jsonDecode(refreshResponse.body)['access_token'];
        if (newToken != null) {
          await _storage.write(key: 'access_token', value: newToken);
          return await fetchUserPlan();
        }
      }
      return null;
    } catch (e) {
      print('Error refreshing token: $e');
      return null;
    }
  }

  // Check if user has active trial
  static bool isTrialActive(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return false;
    return userPlan['status'] != 'inactive';
  }

  // Check if trial is expired (status is inactive)
  static bool isTrialExpired(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return false;
    return userPlan['status'] == 'inactive';
  }

  // Get days remaining in trial
  static int getDaysRemaining(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return 0;
    return userPlan['days_remaining'] ?? 0;
  }

  // Get trial expiry date
  static DateTime? getTrialExpiryDate(Map<String, dynamic>? userPlan) {
    if (userPlan == null || userPlan['expiry_date'] == null) return null;
    try {
      return DateTime.parse(userPlan['expiry_date']);
    } catch (e) {
      return null;
    }
  }

  // Check if exactly 24 hours remain before expiration
  static bool shouldShow24HourReminder(Map<String, dynamic>? userPlan) {
    if (userPlan == null || userPlan['status'] == 'inactive') return false;

    final expiryDate = getTrialExpiryDate(userPlan);
    if (expiryDate == null) return false;

    final now = DateTime.now();
    final timeUntilExpiry = expiryDate.difference(now);

    // Check if we're within the 24-hour window (23-25 hours to account for timing variations)
    final hoursRemaining = timeUntilExpiry.inHours;
    final shouldShow = hoursRemaining >= 23 && hoursRemaining <= 25;

    print('Trial System: Hours until expiry: $hoursRemaining');
    print('Trial System: 24-hour reminder should show: $shouldShow');

    return shouldShow;
  }

  // Get precise time remaining until expiration
  static Duration? getTimeUntilExpiry(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return null;

    final expiryDate = getTrialExpiryDate(userPlan);
    if (expiryDate == null) return null;

    final now = DateTime.now();
    final timeRemaining = expiryDate.difference(now);

    return timeRemaining.isNegative ? Duration.zero : timeRemaining;
  }

  // Schedule trial expiry notification
  static Future<void> scheduleTrialExpiryNotification(Map<String, dynamic> userPlan) async {
    final expiryDate = getTrialExpiryDate(userPlan);
    if (expiryDate == null) return;

    final notificationTime = expiryDate.subtract(const Duration(days: 1));
    final now = DateTime.now();

    // Only schedule if notification time is in the future
    if (notificationTime.isAfter(now)) {
      // Check if we already sent notification today
      final lastNotification = await _storage.read(key: _lastNotificationKey);
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      if (lastNotification != today) {
        await _scheduleNotification(notificationTime);
        await _storage.write(key: _lastNotificationKey, value: today);
      }
    }
  }

  // Schedule the actual notification
  static Future<void> _scheduleNotification(DateTime scheduledTime) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'trial_expiry_channel',
      'Trial Expiry Notifications',
      channelDescription: 'Notifications for trial plan expiry',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _notificationsPlugin.zonedSchedule(
      0,
      'Trial Expiring Soon',
      'Your trial expires tomorrow. Upgrade now to continue enjoying all features!',
      tz.TZDateTime.from(scheduledTime, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  // Show immediate trial expiry warning - ONLY at 24-hour mark or when expired
  static void showTrialExpiryWarning(BuildContext context, Map<String, dynamic> userPlan) {
    final isExpired = isTrialExpired(userPlan);
    final should24HourReminder = shouldShow24HourReminder(userPlan);

    print('Trial System: Checking warning conditions...');
    print('Trial System: Is expired: $isExpired');
    print('Trial System: Should show 24-hour reminder: $should24HourReminder');

    if (isExpired) {
      print('Trial System: Showing expired trial warning');
      _showExpiredTrialSnackBar(context);
    } else if (should24HourReminder) {
      print('Trial System: Showing 24-hour reminder');
      _show24HourReminderSnackBar(context);
    } else {
      print('Trial System: No warning needed at this time');
    }
  }

  static void _showExpiredTrialSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.warning, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Your trial has expired. Upgrade to continue using all features!',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Upgrade',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to upgrade screen
          },
        ),
      ),
    );
  }

  static void _show24HourReminderSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.schedule, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Your trial expires in 24 hours! Upgrade now to continue enjoying all features.',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Upgrade Now',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to upgrade screen
          },
        ),
      ),
    );
  }



  // Clear trial data (for logout)
  static Future<void> clearTrialData() async {
    await _storage.delete(key: _trialShownKey);
    await _storage.delete(key: _lastNotificationKey);
  }
}
