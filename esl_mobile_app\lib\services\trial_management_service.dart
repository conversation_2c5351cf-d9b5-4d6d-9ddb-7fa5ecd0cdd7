import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

class TrialManagementService {
  static const String _baseUrl = 'https://talktoai.in';
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _trialShownKey = 'trial_welcome_shown';
  static const String _lastNotificationKey = 'last_trial_notification';
  
  static final FlutterLocalNotificationsPlugin _notificationsPlugin = 
      FlutterLocalNotificationsPlugin();

  // Initialize notification service
  static Future<void> initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
    );
    
    await _notificationsPlugin.initialize(initializationSettings);
  }

  // Check if trial welcome popup should be shown
  static Future<bool> shouldShowTrialWelcome() async {
    final shown = await _storage.read(key: _trialShownKey);
    return shown == null;
  }

  // Mark trial welcome as shown
  static Future<void> markTrialWelcomeShown() async {
    await _storage.write(key: _trialShownKey, value: 'true');
  }

  // Fetch user plan details
  static Future<Map<String, dynamic>?> fetchUserPlan() async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) return null;

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/get_user_plan'),
        headers: {'Authorization': 'Bearer $token'},
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        return jsonDecode(utf8.decode(response.bodyBytes));
      } else if (response.statusCode == 401) {
        // Try to refresh token
        final refreshedPlan = await _refreshTokenAndRetry();
        return refreshedPlan;
      }
      return null;
    } catch (e) {
      print('Error fetching user plan: $e');
      return null;
    }
  }

  // Refresh token and retry plan fetch
  static Future<Map<String, dynamic>?> _refreshTokenAndRetry() async {
    final refreshToken = await _storage.read(key: 'refresh_token');
    if (refreshToken == null) return null;

    try {
      final refreshResponse = await http.post(
        Uri.parse('https://talktoai.in:8001/refresh-token'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'refresh_token': refreshToken}),
      ).timeout(const Duration(seconds: 10));

      if (refreshResponse.statusCode == 200) {
        final newToken = jsonDecode(refreshResponse.body)['access_token'];
        if (newToken != null) {
          await _storage.write(key: 'access_token', value: newToken);
          return await fetchUserPlan();
        }
      }
      return null;
    } catch (e) {
      print('Error refreshing token: $e');
      return null;
    }
  }

  // Check if user has active trial
  static bool isTrialActive(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return false;
    return userPlan['status'] != 'inactive';
  }

  // Check if trial is expired (status is inactive)
  static bool isTrialExpired(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return false;
    return userPlan['status'] == 'inactive';
  }

  // Get days remaining in trial
  static int getDaysRemaining(Map<String, dynamic>? userPlan) {
    if (userPlan == null) return 0;
    return userPlan['days_remaining'] ?? 0;
  }

  // Get trial expiry date
  static DateTime? getTrialExpiryDate(Map<String, dynamic>? userPlan) {
    if (userPlan == null || userPlan['expiry_date'] == null) return null;
    try {
      return DateTime.parse(userPlan['expiry_date']);
    } catch (e) {
      return null;
    }
  }

  // Schedule trial expiry notification
  static Future<void> scheduleTrialExpiryNotification(Map<String, dynamic> userPlan) async {
    final expiryDate = getTrialExpiryDate(userPlan);
    if (expiryDate == null) return;

    final notificationTime = expiryDate.subtract(const Duration(days: 1));
    final now = DateTime.now();

    // Only schedule if notification time is in the future
    if (notificationTime.isAfter(now)) {
      // Check if we already sent notification today
      final lastNotification = await _storage.read(key: _lastNotificationKey);
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      if (lastNotification != today) {
        await _scheduleNotification(notificationTime);
        await _storage.write(key: _lastNotificationKey, value: today);
      }
    }
  }

  // Schedule the actual notification
  static Future<void> _scheduleNotification(DateTime scheduledTime) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'trial_expiry_channel',
      'Trial Expiry Notifications',
      channelDescription: 'Notifications for trial plan expiry',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _notificationsPlugin.zonedSchedule(
      0,
      'Trial Expiring Soon',
      'Your trial expires tomorrow. Upgrade now to continue enjoying all features!',
      tz.TZDateTime.from(scheduledTime, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  // Show immediate trial expiry warning
  static void showTrialExpiryWarning(BuildContext context, Map<String, dynamic> userPlan) {
    final daysRemaining = getDaysRemaining(userPlan);
    final isExpired = isTrialExpired(userPlan);
    
    if (isExpired) {
      _showExpiredTrialSnackBar(context);
    } else if (daysRemaining <= 1) {
      _showUrgentTrialSnackBar(context);
    } else if (daysRemaining <= 3) {
      _showTrialReminderSnackBar(context, daysRemaining);
    }
  }

  static void _showExpiredTrialSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.warning, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Your trial has expired. Upgrade to continue using all features!',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Upgrade',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to upgrade screen
          },
        ),
      ),
    );
  }

  static void _showUrgentTrialSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.schedule, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Your trial expires today! Upgrade now to avoid interruption.',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Upgrade',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to upgrade screen
          },
        ),
      ),
    );
  }

  static void _showTrialReminderSnackBar(BuildContext context, int daysRemaining) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Your trial expires in $daysRemaining days. Consider upgrading!',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Upgrade',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to upgrade screen
          },
        ),
      ),
    );
  }

  // Clear trial data (for logout)
  static Future<void> clearTrialData() async {
    await _storage.delete(key: _trialShownKey);
    await _storage.delete(key: _lastNotificationKey);
  }
}
