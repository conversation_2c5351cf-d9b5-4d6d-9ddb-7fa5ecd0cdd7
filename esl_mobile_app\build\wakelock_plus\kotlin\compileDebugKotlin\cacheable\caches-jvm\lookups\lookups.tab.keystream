  Any    Array    BasicMessageChannel    BinaryMessenger    Boolean    Byte    	ByteArray    ByteArrayOutputStream    
ByteBuffer    DoubleArray    Int    IntArray    IsEnabledMessage    JvmOverloads    List    Log    	LongArray    Map    MessageCodec    StandardMessageCodec    String    Suppress    	Throwable    
ToggleMessage    WakelockPlusApi    WakelockPlusFlutterError    WakelockPlusMessagesPigeonCodec    all    
contentEquals    deepEqualsWakelockPlusMessages    fromList    getValue    indices    
isNotEmpty    	javaClass    lazy    let    listOf    provideDelegate    run    	wrapError    
wrapResult    Any IsEnabledMessage  Boolean IsEnabledMessage  	Companion IsEnabledMessage  Int IsEnabledMessage  IsEnabledMessage IsEnabledMessage  List IsEnabledMessage  deepEqualsWakelockPlusMessages IsEnabledMessage  enabled IsEnabledMessage  fromList IsEnabledMessage  listOf IsEnabledMessage  toList IsEnabledMessage  IsEnabledMessage IsEnabledMessage.Companion  deepEqualsWakelockPlusMessages IsEnabledMessage.Companion  fromList IsEnabledMessage.Companion  listOf IsEnabledMessage.Companion  Any 
ToggleMessage  Boolean 
ToggleMessage  	Companion 
ToggleMessage  Int 
ToggleMessage  List 
ToggleMessage  
ToggleMessage 
ToggleMessage  deepEqualsWakelockPlusMessages 
ToggleMessage  enable 
ToggleMessage  fromList 
ToggleMessage  listOf 
ToggleMessage  toList 
ToggleMessage  
ToggleMessage ToggleMessage.Companion  deepEqualsWakelockPlusMessages ToggleMessage.Companion  fromList ToggleMessage.Companion  listOf ToggleMessage.Companion  Any WakelockPlusApi  BasicMessageChannel WakelockPlusApi  BinaryMessenger WakelockPlusApi  	Companion WakelockPlusApi  IsEnabledMessage WakelockPlusApi  JvmOverloads WakelockPlusApi  List WakelockPlusApi  MessageCodec WakelockPlusApi  String WakelockPlusApi  	Throwable WakelockPlusApi  
ToggleMessage WakelockPlusApi  WakelockPlusApi WakelockPlusApi  WakelockPlusMessagesPigeonCodec WakelockPlusApi  codec WakelockPlusApi  getValue WakelockPlusApi  	isEnabled WakelockPlusApi  
isNotEmpty WakelockPlusApi  lazy WakelockPlusApi  listOf WakelockPlusApi  provideDelegate WakelockPlusApi  run WakelockPlusApi  setUp WakelockPlusApi  toggle WakelockPlusApi  	wrapError WakelockPlusApi  BasicMessageChannel WakelockPlusApi.Companion  WakelockPlusMessagesPigeonCodec WakelockPlusApi.Companion  codec WakelockPlusApi.Companion  getValue WakelockPlusApi.Companion  
isNotEmpty WakelockPlusApi.Companion  lazy WakelockPlusApi.Companion  listOf WakelockPlusApi.Companion  provideDelegate WakelockPlusApi.Companion  run WakelockPlusApi.Companion  setUp WakelockPlusApi.Companion  	wrapError WakelockPlusApi.Companion  code WakelockPlusFlutterError  details WakelockPlusFlutterError  message WakelockPlusFlutterError  IsEnabledMessage WakelockPlusMessagesPigeonCodec  
ToggleMessage WakelockPlusMessagesPigeonCodec  fromList WakelockPlusMessagesPigeonCodec  let WakelockPlusMessagesPigeonCodec  	readValue WakelockPlusMessagesPigeonCodec  
writeValue WakelockPlusMessagesPigeonCodec  Activity android.app  window android.app.Activity  Log android.util  getStackTraceString android.util.Log  
WindowManager android.view  addFlags android.view.Window  
attributes android.view.Window  
clearFlags android.view.Window  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  flags 'android.view.WindowManager.LayoutParams  Activity "dev.fluttercommunity.plus.wakelock  
ActivityAware "dev.fluttercommunity.plus.wakelock  ActivityPluginBinding "dev.fluttercommunity.plus.wakelock  	Exception "dev.fluttercommunity.plus.wakelock  
FlutterPlugin "dev.fluttercommunity.plus.wakelock  IsEnabledMessage "dev.fluttercommunity.plus.wakelock  NoActivityException "dev.fluttercommunity.plus.wakelock  
ToggleMessage "dev.fluttercommunity.plus.wakelock  Wakelock "dev.fluttercommunity.plus.wakelock  WakelockPlusApi "dev.fluttercommunity.plus.wakelock  WakelockPlusPlugin "dev.fluttercommunity.plus.wakelock  
WindowManager "dev.fluttercommunity.plus.wakelock  setUp "dev.fluttercommunity.plus.wakelock  FlutterPluginBinding 0dev.fluttercommunity.plus.wakelock.FlutterPlugin  IsEnabledMessage +dev.fluttercommunity.plus.wakelock.Wakelock  NoActivityException +dev.fluttercommunity.plus.wakelock.Wakelock  
WindowManager +dev.fluttercommunity.plus.wakelock.Wakelock  activity +dev.fluttercommunity.plus.wakelock.Wakelock  enabled +dev.fluttercommunity.plus.wakelock.Wakelock  	isEnabled +dev.fluttercommunity.plus.wakelock.Wakelock  toggle +dev.fluttercommunity.plus.wakelock.Wakelock  Wakelock 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  WakelockPlusApi 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  onAttachedToActivity 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  onDetachedFromActivity 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  setUp 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  wakelock 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BasicMessageChannel io.flutter.plugin.common  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  MessageCodec io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  StandardMethodCodec io.flutter.plugin.common  MessageHandler ,io.flutter.plugin.common.BasicMessageChannel  Reply ,io.flutter.plugin.common.BasicMessageChannel  setMessageHandler ,io.flutter.plugin.common.BasicMessageChannel  <SAM-CONSTRUCTOR> ;io.flutter.plugin.common.BasicMessageChannel.MessageHandler  reply 2io.flutter.plugin.common.BasicMessageChannel.Reply  Any -io.flutter.plugin.common.StandardMessageCodec  IsEnabledMessage -io.flutter.plugin.common.StandardMessageCodec  List -io.flutter.plugin.common.StandardMessageCodec  
ToggleMessage -io.flutter.plugin.common.StandardMessageCodec  fromList -io.flutter.plugin.common.StandardMessageCodec  let -io.flutter.plugin.common.StandardMessageCodec  	readValue -io.flutter.plugin.common.StandardMessageCodec  readValueOfType -io.flutter.plugin.common.StandardMessageCodec  
writeValue -io.flutter.plugin.common.StandardMessageCodec  ByteArrayOutputStream java.io  write java.io.ByteArrayOutputStream  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  
simpleName java.lang.Class  
ByteBuffer java.nio  Array kotlin  	ByteArray kotlin  DoubleArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Lazy kotlin  	LongArray kotlin  Nothing kotlin  Suppress kotlin  	Throwable kotlin  getValue kotlin  lazy kotlin  let kotlin  run kotlin  hashCode 
kotlin.Any  toString 
kotlin.Any  get kotlin.Array  indices kotlin.Array  size kotlin.Array  not kotlin.Boolean  
contentEquals kotlin.ByteArray  
contentEquals kotlin.DoubleArray  and 
kotlin.Int  toByte 
kotlin.Int  
contentEquals kotlin.IntArray  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  
contentEquals kotlin.LongArray  
isNotEmpty 
kotlin.String  plus 
kotlin.String  cause kotlin.Throwable  	javaClass kotlin.Throwable  message kotlin.Throwable  toString kotlin.Throwable  List kotlin.collections  Map kotlin.collections  all kotlin.collections  
contentEquals kotlin.collections  contentEqualsNullable kotlin.collections  getValue kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  get kotlin.collections.List  hashCode kotlin.collections.List  indices kotlin.collections.List  let kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  all kotlin.collections.Map  containsKey kotlin.collections.Map  get kotlin.collections.Map  size kotlin.collections.Map  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  JvmOverloads 
kotlin.jvm  	javaClass 
kotlin.jvm  IntRange 
kotlin.ranges  all kotlin.ranges.IntRange  KClass kotlin.reflect  
KProperty1 kotlin.reflect  all kotlin.sequences  all kotlin.text  
contentEquals kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  